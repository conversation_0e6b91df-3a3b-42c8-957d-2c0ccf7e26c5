# \u8FD9\u4E2A\u6587\u4EF6\u91CC\u9762\u6C47\u603B\u8BB0\u5F55\u5404\u4E2A\u5DE5\u7A0B\u914D\u7F6E\u7684\u7B2C\u4E09\u65B9\u76F8\u5173\u914D\u7F6E\uFF0C\u540E\u7EED\u6240\u6709\u6D89\u53CA\u5230\u7B2C\u4E09\u65B9\u7684\u63A5\u53E3\u4FE1\u606F\u90FD\u914D\u7F6E\u5230\u5176\u4E2D
# \u6A21\u677F\u4E2D\u9ED8\u8BA4\u503C\u90FD\u662F\u7A7A\uFF0C\u9879\u76EE\u4E0A\u90E8\u7F72\u7684\u65F6\u5019\u518D\u53BB\u586B\u5199\u5BF9\u5E94\u7684\u503C
# \u4E3A\u4E86\u907F\u514D\u51B2\u7A81\u540E\u7EED\u914D\u7F6E\u9879\u547D\u540D\u8981\u6C42\uFF1A${project.name}.xxx.xxx=
## search(search.ymal\uFF0Csearch-new.properties)
# 20241008 - \u891A\u5DDD\u5B9D - \u63A8\u9001\u65E5\u5FD7\u5230\u95E8\u6237\u7684URL
search.log.push.menhu.url=
# 20241008 - \u891A\u5DDD\u5B9D - \u63A8\u9001\u65E5\u5FD7\u5230\u95E8\u6237\u7684thirdPartyName
search.log.push.menhu.thirdPartyName=
# 20241017 - \u891A\u5DDD\u5B9D - \u9AD8\u65B0kafka\u6D88\u8D39
search.foothold.consumer.kafka.gx.bootStrapServers=
search.foothold.consumer.kafka.gx.groupId=
search.foothold.consumer.kafka.gx.topic=
# 20241107 - \u5F20\u9633 - \u6280\u4FA6\u6570\u636Ekafka\u6D88\u8D39
search.consumer.kafka.jz.bootStrapServers=
search.consumer.kafka.jz.groupId=
search.consumer.kafka.jz.topic=
# 20241108 - \u848B\u4FCA\u6770 - \u8F68\u8FF9kafka\u6D88\u8D39(\u5FB7\u9633\u7684search-new.properties\u4E2D\u589E\u52A0)
search.consumer.kafka.trajectory.bootStrapServers=
search.consumer.kafka.trajectory.groupId=
search.consumer.kafka.trajectory.topic=
# 20241226 - \u891A\u5DDD\u5B9D - AI\u76F8\u5173\u914D\u7F6E
search.ai.config.summary.host=
search.ai.config.summary.appId=
search.ai.config.summary.userName=
search.ai.config.summary.token=
search.ai.config.summary.defaultTenantId=
# AI\u5206\u6790
search.ai.config.analysis.host=
search.ai.config.analysis.appId=
search.ai.config.analysis.userName=
search.ai.config.analysis.token=
search.ai.config.analysis.defaultTenantId=
# ES\u914D\u7F6E
warning.subscribe.searchRepositoryHost=
warning.subscribe.searchRepositoryPort=
warning.subscribe.searchRepositoryUser=
warning.subscribe.searchRepositoryPassword=
# \u662F\u5426\u542F\u52A8kerberos\u8BA4\u8BC1
warning.subscribe.searchRepositoryKerberos=
# \u542F\u52A8kerberos\u8BA4\u8BC1\u65F6\uFF0C\u4E91\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
warning.subscribe.searchRepositoryCloudType=
# \u542F\u52A8kerberos\u8BA4\u8BC1\u65F6\uFF0CPrincipal\u4E0D\u80FD\u4E3A\u7A7A
warning.subscribe.searchRepositoryPrincipal=
# \u542F\u52A8kerberos\u8BA4\u8BC1\u65F6\uFF0Ckrb5Path\u4E0D\u80FD\u4E3A\u7A7A
warning.subscribe.searchRepositoryKrb5Path=
# \u542F\u52A8kerberos\u8BA4\u8BC1\u65F6\uFF0CuserKeytabPath\u4E0D\u80FD\u4E3A\u7A7A
warning.subscribe.searchRepositoryUserKeytabPath=
# 20240119 \u6D88\u606F\u53D1\u9001\u7684Kafka\u670D\u52A1\u5668\u5730\u5740
search.face.recognition.sendMessage.bootServer=
# \u6863\u6848\u66F4\u65B0\u6D88\u606F\u53D1\u9001\u7684kafka
search.producer.kafka.archive.bootServer=
# \u6863\u6848\u66F4\u65B0\u6D88\u606F\u6D88\u8D39\u7684kafka
search.consumer.kafka.archive.bootServer=
# \u9AD8\u65B0\u80FD\u529B\u5E73\u53F0\u4E2D\u201C\u8F66\u724C\u53F7\u67E5\u8BE2\u63A5\u53E3\uFF08\u6210\u90FD\u5E02\uFF09\u201D
search.api.chengduchepai.clientKey=
search.api.chengduchepai.clientName=
search.api.chengduchepai.publicKey=
search.api.chengduchepai.baseUri=
search.api.chengduchepai.path=
# \u9AD8\u65B0\u80FD\u529B\u5E73\u53F0\u4E2D\u201C\u6839\u636E\u8EAB\u4EFD\u8BC1\u53F7\u67E5\u8BE2\u6210\u90FD\u5E02\u5E38\u9A7B\u4EBA\u53E3\u4FE1\u606F\u201D
search.api.shenfenzheng.clientKey=
search.api.shenfenzheng.clientName=
search.api.shenfenzheng.publicKey=
search.api.shenfenzheng.baseUri=
search.api.shenfenzheng.path=
# \u9AD8\u65B0\u80FD\u529B\u5E73\u53F0\u4E2D\u201C\u8F66\u724C\u53F7\u67E5\u8BE2\u63A5\u53E3\uFF08\u56DB\u5DDD\u7701\uFF09\u201D
search.api.sichuanchepai.clientKey=
search.api.sichuanchepai.clientName=
search.api.sichuanchepai.publicKey=
search.api.sichuanchepai.baseUri=
search.api.sichuanchepai.path=
# \u4E1A\u52A1\u5E93\u914D\u7F6E
dwd.repository.config.esRepositoryHost=
dwd.repository.config.esRepositoryPort=
dwd.repository.config.esRepositoryUserName=
dwd.repository.config.esRepositoryPassword=
dwd.repository.config.hiveRepositoryHost=
dwd.repository.config.hiveRepositoryPort=
dwd.repository.config.hiveRepositoryUserName=
dwd.repository.config.hiveRepositoryPassword=
###################################################################
## projects(projects.ymal\uFF0Cprojects.crj.properties)
# \u54EA\u4E2A\u9879\u76EE\u7528\u8FD9\u4E2A\u529F\u80FD\uFF0C\u6CF8\u5DDE\u5BF9\u5E94\u503C\u4E3A\u201Clz\u201D
com.trs.crj.system.area=
## \u6CF8\u5DDE\u51FA\u5165\u5883\u4FE1\u606F\u68C0\u6D4B\u914D\u7F6E
# \u83B7\u53D6\u8BC1\u4E66\u7B7E\u540Durl
com.trs.crj.system.certUrl=
# \u7F51\u5173webservice\u63A5\u53E3\u63CF\u8FF0\u5730\u5740
com.trs.crj.system.wsdl=
# \u5BA2\u6237\u7AEFip
com.trs.crj.system.clientIp=
# \u5BA2\u6237\u7AEF\u8BC1\u4E66\u7F16\u53F7
com.trs.crj.system.clientZsbh=
# \u5BA2\u6237\u7AEF\u5355\u4F4D\u7F16\u53F7
com.trs.crj.system.clientDwbh=
# \u5BA2\u6237\u7AEF\u5355\u4F4D\u540D\u79F0
com.trs.crj.system.clientDwmc=
# \u5BA2\u6237\u7AEF\u59D3\u540D
com.trs.crj.system.clientXm=
# \u5BA2\u6237\u7AEF\u8EAB\u4EFD\u8BC1\u53F7
com.trs.crj.system.clientSfzh=
# \u6CF8\u5DDE\u51FA\u5165\u5883\u53D1\u9001\u77ED\u4FE1\u7684\u76EE\u6807\uFF0Ckey\u4E3A\u5730\u57DF\u7F16\u7801\uFF0Cvalue\u4E3A\u76EE\u6807\u7684\u624B\u673A\u53F7\u5217\u8868\uFF08\u591A\u4E2A\u9017\u53F7\u5206\u5272\uFF09
com.trs.crj.lz.send.phone.mapping=
# XMKFB-3470 \u77ED\u4FE1\u63A5\u6536\u65B9\uFF08\u624B\u673A\u53F7\uFF09\uFF0C\u591A\u4E2A\u9017\u53F7\u5206\u5272
com.trs.crj.rzxx.system.phone=
# \u54EA\u4E2A\u9879\u76EE\u7528\u8FD9\u4E2A\u529F\u80FD\uFF0C\u6CF8\u5DDE\u5BF9\u5E94\u503C\u4E3Alz,\u5E7F\u5B89\u4E3Aga,\u81EA\u8D21\u4E3Azg
com.trs.bigscreen.system.area=
# 20240912 - \u891A\u5DDD\u5B9D - \u5357\u5145\u7684\u503C\u73ED\u63A5\u53E3URL
com.trs.bigscreen.sync.duty.nc.url=
# 20241008 - \u891A\u5DDD\u5B9D - \u624B\u52A8\u914D\u7F6E\u7684\u9700\u8981\u540C\u6B65\u7684\u503C\u73ED\u4EBA\u5458\u4FE1\u606F
com.trs.bigscreen.sync.duty.nc.custom.dutyUser=[]
# 20241008 - \u891A\u5DDD\u5B9D - \u9700\u8981\u540C\u6B65\u5730\u57DF\u7F16\u7801\u7684\u503C\u73ED\u4EBA\u5458\u4FE1\u606F\uFF0C\u591A\u4E2A\u9017\u53F7\u5206\u5272\uFF08\u4EE5\u5176\u5F00\u5934\u6216\u7B49\u4E8E\u5176\u7684\u6570\u636E\uFF09
com.trs.bigscreen.sync.duty.nc.supportCode=
# 20241202 - \u891A\u5DDD\u5B9D - XMKFB-5474 - \u5357\u5145\u6839\u636E\u5730\u57DF\u7F16\u7801\u6620\u5C04\u5BF9\u5E94\u7684\u5355\u4F4D\u540D\uFF0Ckey\u4E3A\u5730\u57DF\u7F16\u7801\uFF0Cvalue\u4E3A\u5355\u4F4D\u540D
com.trs.bigscreen.sync.duty.nc.code.unitName.mapping={}
# 20240918 - \u891A\u5DDD\u5B9D - \u5E7F\u5B89\u7B2C\u4E09\u65B9\u63A5\u53E3\u914D\u7F6E
com.trs.bigscreen.sync.duty.ga.authCode=
com.trs.bigscreen.sync.duty.ga.authSecret=
com.trs.bigscreen.sync.duty.ga.appCode=
com.trs.bigscreen.sync.duty.ga.ResourceName=
com.trs.bigscreen.sync.duty.ga.tokenUrl=
com.trs.bigscreen.sync.duty.ga.queryUrl=
com.trs.bigscreen.sync.duty.ga.kafka.bootStrapServers=
com.trs.bigscreen.sync.duty.ga.kafka.groupId=
com.trs.bigscreen.sync.duty.ga.kafka.topic=
com.trs.bigscreen.sync.duty.ga.kafka.username=
com.trs.bigscreen.sync.duty.ga.kafka.password=
# 20241205 - \u891A\u5DDD\u5B9D - \u81EA\u8D21\u52E4\u52A1\u62A5\u5907\u7CFB\u7EDF\u76F8\u5173\u4FE1\u606F
com.trs.bigscreen.sync.zg.qwbb.appid=
com.trs.bigscreen.sync.zg.qwbb.secretkey=
com.trs.bigscreen.sync.zg.qwbb.host=
# 20250306 - \u5F20\u9633 - \u7701\u5385\u878D\u5408\u901A\u8BAF\u7B2C\u4E09\u65B9\u63A5\u53E3\u914D\u7F6E
com.trs.bigscreen.sync.rxtx.host=
com.trs.bigscreen.sync.rxtx.deviceStatu.key=
###################################################################
# 20250313 - \u891A\u5DDD\u5B9D - AI\u4FE1\u606F\u63D0\u53D6\u5DE5\u5177
# AI\u4FE1\u606F\u63D0\u53D6
spring.trs.ai.config.infoExtract.host=
spring.trs.ai.config.infoExtract.appId=
spring.trs.ai.config.infoExtract.userName=
spring.trs.ai.config.infoExtract.token=
spring.trs.ai.config.infoExtract.defaultTenantId=
spring.trs.ai.config.infoExtract.needPrintLog=true
# 20250326 - \u5F20\u9633 - \u5B89\u5DE1\u6570\u636E\u540C\u6B65\u65B9\u5F0F\u914D\u7F6E[\u89C6\u56FE\uFF1Aview\uFF08\u5E7F\u5B89\uFF09  API\uFF1Aapi\uFF08\u81EA\u8D21\uFF09]
com.trs.bigscreen.system.qinwu.utils.key=
# 20250326 - \u5F20\u9633 - \u5B89\u5DE1\u6570\u636E\u6E90\u76F8\u5173\u914D\u7F6E
spring.datasource.dynamic.datasource.ga-jcgl.username=\u8D26\u6237
spring.datasource.dynamic.datasource.ga-jcgl.password=\u5BC6\u7801
spring.datasource.dynamic.datasource.ga-jcgl.lazy=true
spring.datasource.dynamic.datasource.ga-jcgl.url=********************************
spring.datasource.dynamic.datasource.ga-jcgl.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ga-jcgl.druid.max-wait=10000
spring.datasource.dynamic.datasource.ga-jcgl.druid.validation-query=select 1
spring.datasource.dynamic.datasource.ga-jcgl.druid.validation-query-timeout=200
spring.datasource.dynamic.datasource.ga-jcgl.druid.initial-size=5
spring.datasource.dynamic.datasource.ga-jcgl.druid.max-active=5
spring.datasource.dynamic.datasource.ga-jcgl.druid.min-idle=5
spring.datasource.dynamic.datasource.ga-qwgl.username=\u8D26\u6237
spring.datasource.dynamic.datasource.ga-qwgl.password=\u5BC6\u7801
spring.datasource.dynamic.datasource.ga-qwgl.lazy=true
spring.datasource.dynamic.datasource.ga-qwgl.url=********************************
spring.datasource.dynamic.datasource.ga-qwgl.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.ga-qwgl.druid.max-wait=10000
spring.datasource.dynamic.datasource.ga-qwgl.druid.validation-query=select 1
spring.datasource.dynamic.datasource.ga-qwgl.druid.validation-query-timeout=200
spring.datasource.dynamic.datasource.ga-qwgl.druid.initial-size=5
spring.datasource.dynamic.datasource.ga-qwgl.druid.max-active=5
spring.datasource.dynamic.datasource.ga-qwgl.druid.min-idle=5
# 20250415 - \u8096\u8C6A - \u68C0\u7D22\u8D44\u6E90\u53F0\u8D26\u76EE\u5F55\u5BFC\u5165ai\u5224\u65AD\u76F8\u5173\u4FE1\u606F
search.ai.config.tableImport.enable=false
search.ai.config.tableImport.host=
search.ai.config.tableImport.appId=
search.ai.config.tableImport.userName=
search.ai.config.tableImport.token=
search.ai.config.tableImport.defaultTenantId=
search.ai.config.tableImport.needPrintLog=true
search.ai.config.tableImport.callTimeout=300
search.ai.config.tableImport.connectTimeout=300
# 20250508 - \u5F20\u9633 - \u8B66\u60C5AI\u5206\u6790\u6D88\u606F\u53D1\u9001\u914D\u7F6E
search.producer.kafka.sentiment.ai.answer.enable=false
search.producer.kafka.sentiment.ai.answer.topic=nlp_chat_data_import_topic
# \u9879\u76EE\u73AF\u5883\u9700\u8981\u6839\u636E\u5B9E\u9645\u4F7F\u7528\u60C5\u51B5\u8C03\u6574\u6B64\u5730\u5740
search.producer.kafka.sentiment.ai.answer.bootServer=2
# 20250508 - \u5F20\u9633 - \u8B66\u60C5ai\u5206\u6790\u76F8\u5173\u914D\u7F6E
search.sentiment.ai.analysis.host=
search.sentiment.ai.analysis.appId=
search.sentiment.ai.analysis.userId=
# \u5927\u6A21\u578B\u54CD\u5E94\u8D85\u65F6\u65F6\u95F4\uFF08\u79D2\uFF09
search.sentiment.ai.analysis.timeout=120
search.sentiment.ai.analysis.needPrintLog=true
###################################################################
# \u7701\u5385\u5E73\u53F0host
intelligence.province.clue.host=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684\u7528\u6237\u540D
intelligence.renwu.shizhen.username=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684\u5BC6\u7801
intelligence.renwu.shizhen.password=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684type\uFF08\u8C03\u7528\u65B9\u540D\u79F0\uFF09
intelligence.renwu.shizhen.type=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684tgt\u8DDFtickets\u83B7\u53D6URL
intelligence.renwu.shizhen.tickets.url=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684\u8DF3\u8F6C\u5730\u5740(\u96C6\u6210\u767B\u5F55)
intelligence.renwu.shizhen.validate.url=
# 20240624 \u891A\u5DDD\u5B9D \u4EFB\u52A1\u89C6\u4FA6\u7684\u8DF3\u8F6C\u5730\u5740(\u76EE\u6807\u5730\u5740)
intelligence.renwu.shizhen.last.url=
intelligence.renwu.dsjhc.host=
intelligence.renwu.dsjhc.policeNo=
intelligence.renwu.dsjhc.userId=
intelligence.renwu.dsjhc.appId=
intelligence.renwu.dsjhc.subId=
intelligence.renwu.dsjhc.senderId=
intelligence.renwu.dsjhc.groupId=
intelligence.renwu.dsjhc.areaCode=
# 20250520 - \u8096\u8C6A - \u589E\u52A0\u7701\u5385\u60C5\u6307\u878D\u5408\u901A\u8BAF\u7684\u4EBA\u5458\u914D\u7F6E
projects.qz.rh.personList=
# 20250606 - \u891A\u5DDD\u5B9D - \u589E\u52A0\u5BF9\u5E94\u56DE\u6D41\u5931\u8D25\u6D88\u606F\u53D1\u9001
intelligence.st.data.err.message.to.phone=
intelligence.st.data.err.message.end=

