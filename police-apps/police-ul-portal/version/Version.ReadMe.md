# 统一登录门户
#  18.1
- XMKFB-8433 高新-【线索池】新增重复报警和民转型模型


## 17.2
- XMKFB-8402 【高新】门户线索池，“五失一偏风险人员”的预警原因显示有误

## 17.1
- XMKFB-8269 后-【高新】- 门户用户同步逻辑优化

## v RC20250416
> XMKFB-7858 高新-【统一门户】登录时查询用户相关的信息使用门户自己的数据库

## v13.3 发版日志
> XMKFB-6424 合 -【高新】- 线索池-涉黄模型新增一个条件筛选项

## v13.1 发版日志
> XMKFB-6109 高新门户无法使用身份证号作为账号登录
> XMKFB-5856 - 【高新】线索池，涉毒人员聚集线索详情筛选人员轨迹数据为空


## v12.4 发版日志
> XMKFB-5858 【高新】涉毒人员聚集线索，线索列表无法搜索出涉毒人员聚集线索
> XMKFB-5856 【高新】线索池，涉毒人员聚集线索详情筛选人员轨迹数据为空
> XMKFB-5972 前-【高新】线索池列表详情，轨迹列表默认时间倒序

## v12.3 发版日志
> XMKFB-5841 【高新】线索池，涉毒人员聚集风险里部分数据涉及人员为0

## v12.2 发版日志
> XMKFB-5574 【高新】线索池，盗窃三车风险预警和扒窃风险预警Tab显示统计的数据量有误
> XMKFB-5579 【智慧云脑】线索池-新增涉毒窝点模型
> XMKFB-5571 【高新】线索池，在线索详情页里研判无效
> XMKFB-5722 门户，后台用户管理禁用用户无效
> XMKFB-5573 【高新】线索池，编辑保存线索人员基本信息无效
> XMKFB-5628 后-【高新】- 涉毒窝点轨迹接口提供/预警原因拼接

## v11.5 发版日志
> XMKFB-5447 【高新】线索池，三失三气驾驶机动车线索里预警原因取的时间有误
> XMKFB-5451 【高新】线索池，线索数据按照编号搜索无效

## V11.4 发版日志
> XMKFB-5406 警情分析专题，涉未成年警情分析统计的学校名称数据应对空值数据进行过滤
> XMKFB-5415 - 【高新】日志审计，预警列表的预警时间显示为了字符串以及预警人员里出现了第三方账号

## RC 20241126
> XMKFB-5305 【调整】-【线索池】查看文档调整逻辑
> XMKFB-5373 【高新】-【线索池】发版问题修复

## V11.3 发版日志
> XMKFB-5139 高新-【线索池】新增一个三失三气人员驾驶机动车模型
> XMKFB-5298【高新】警情专题，各社区警情比对里统计显示的社区有误
> XMKFB-5330 合-【高新】-【智慧云脑】线索池添加研判、修改人员信息功能

## RC 20241118
> XMKFB-5251 【高新】线索池，线索详情里显示的线索编号数据为空

## v11.2 发版日志
> XMKFB-5134 【高新】线索池，线索详情里的模型描述和计分项文字描述应作优化
> XMKFB-5135 高新-【线索池】吸毒人员驾驶机动车轨迹查询调整
> XMKFB-5089 【高新】门户大屏，盗窃案件疑似人员卡片里预警原因显示的时间有误
> XMKFB-5142 【高新】线索池，涉黄人员重点部位、盗窃三车风险预警、扒窃风险预警触发条件未生效
> XMKFB-5197 【高新】线索池，线索详情里基础计分的计分项统计时间有误
> XMKFB-5194 【高新】线索池，线索详情里显示的基础积分数据有误

## v11.1 发版日志
> XMKFB-4934 涉未成年/高校警情分析接口开发
> XMKFB-4943[XMKFB-4937] 后-【高新】-线索池-左侧树接口提供

## v10.5 发版日志
> XMKFB-4820 【高新】线索池，列表按照身份证检索结果为空
> XMKFB-4842 【高新】门户大屏，蝙蝠聊天疑似涉黄人员和涉黄人员重点部位预警列表里的标签数据为空

## v10.4 发版日志> XMKFB-4890 【高新】警情分析专题，公司库别名数据未作判重校验
> XMKFB-4871 警情上图异常
> XMKFB-4873 【自贡】警情分析专题-一个派出所分析中派所管辖单位字段有误，单位人数有误
> XMKFB-4760 【自贡】警情类别异常
> XMKFB-4684  后 - 【高新】警情上图分析获取区域警情数量接口增加重点人员数量
> XMKFB-4305 【高新】警情分析专题，警情高发分析-各社区警情比对统计显示的警情分析数据有误
> XMKFB-4505 【高新】警情分析专题，警情列表按照社区筛选数据无效

## v10.3 发版日志
-XMKFB-4524 门户大屏，相同姓名的不同账号登录系统报错
-XMKFB-4474 后-警情列表徐支持查询区域警情
-XMKFB-4473 后-区域需要支持保存
# 2024-10-18
>XMKFB-4426 后-公司别名维护curd接口开发
>XMKFB-4427 公司警情统计变更
>XMKFB-4544 接口支持 - 警情高发分析-按时间段分析-类别下钻派出所接口调整
>XMKFB-4515 【自贡】派出所民警人均警数分析异常
> XMKFB-4499 【自贡】警情列表性能差

## v9.4发版日志

- XMKFB-3729 【智慧云脑】-门户自定义应用展示方式优化

## v9.3发版日志

- XMKFB-3986 后 - 后端协助处理

## 新增配置项

```yaml
com:
  trs:
    model:
      nameAndDesc: '[{"key":"盗窃警情疑似人员","value":"盗窃警情疑似人员","type":"0"},{"key":"吸毒人员驾驶机动车(主副驾识别)","value":"吸毒人员驾驶机动车(主副驾识别)","type":"1"},{"key":"无证驾驶预警","value":"无证驾驶预警","type":"2"},{"key":"涉黄线索(蝙蝠、SDK)","value":"涉黄线索(蝙蝠、SDK)","type":"5"},{"key":"隐性反邪人员线索挖掘","value":"隐性反邪人员线索挖掘","type":"3"},{"key":"隐性反恐人员线索挖掘","value":"隐性反恐人员线索挖掘","type":"4"}]'
```

## v8.4发版日志
- XMKFB-3340 接口支持 - 首页线索池数据接口支持

## v8.2发版日志
- XMKFB-2974 后-【高新】-线索池优化  

## v7.2 发版日志
# 20240716
>XMKFB-2653、后-日志审计个人排名过滤单位账号

## v6.4 发版日志
# 2024-06-27
>XMKFB-2209、后-派出所民警人均出警数分析点击数据列表，数据重复   
>XMKFB-2261、后-5月，警情高发分析，各社区警情比对，月牙湖社区，纠纷，下钻，经济纠纷列表无数据   

## v6.3 发版日志
# 2024-06-20
>XMKFB-2183、合-合-警情高发分析-各时间段警情比对排序优化 
# 2024-06-19
>XMKFB-1802、合-警情高发分析各社区警情比对警情列表数量对不上    
# 2024-06-18
>XMKFB-2156、后-各社区警情比对，按类别分析无数据。 
# 2024-06-17
>XMKFB-1997、后-警情列表新增处置流程接口
>XMKFB-2103、高新-警情类型二级分类统计的数量没包含三级子分类    

## v6.2 发版日志
# 2024-06-13
>XMKFB-2071、【高新】门户警情专题警情列表弹窗大模型对话增加默认问题 
# 2024-06-12
>XMKFB-2050[XMKFB-2047]、【后】—— 提供相关接口
# 2024-06-11
>XMKFB-1996、后-新增按警员出警分析

## v6.1 发版日志
# 2024-06-04
>XMKFB-1838、后-出警规范分析，列表、按类别、按派出所分析数据错误  
# 2024-06-03
>XMKFB-1875、合-违法犯罪警情开发  

## v5.4 发版日志
# 2024-05-30
>XMKFB-1836、后-日志统计相关接口提供
# 2024-05-29
>XMKFB-1695、后-日志统计相关接口提供
# 2024-05-28
>XMKFB-1761、【前】—— 数据排序涉及到列表的都按警情数量高低倒序排列。   
>XMKFB-1762、【前】—— .警情列表数据不对，例如折线图，选择某天，进入警情列表，列表数量和统计数量对应不上。
# 2024-05-27
>XMKFB-1742、合-【高新】警情分析按派出所统计接口有问题，需要新开接口

## v5.3 发版日志
# 2024-05-22
>XMKFB-1694、后-专题分析相关接口提供
 
## v5.2 发版日志
# 2024-05-16
>XMKFB-1573、提供按警情类别和按派出所分析接口
# 2024-05-14
> XMKFB-1493、警情列表接口提供  
> 优化、警情类别接口返回是否有子  

# nacos配置更新
```
#nacos中数据库多数据源配置新增警情分析数据
gxzhjw-mysqldb:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************************************
    username: ys
    password: 123456
    druid:
    max-wait: 10000
    validation-query: select 1
    validation-query-timeout: 200
    initial-size: 5
    max-active: 5
    min-idle: 5
```

## v5.1 发版日志
# 2024-05-16
>XMKFB-1573、提供按警情类别和按派出所分析接口    
# 2024-05-11
> XMKFB-1334、合-【高新GA】JQ分析专题  
>XMKFB-1468、后-重复报警统计相关接口开发  

# nacos配置更新
```
#nacos中数据库多数据源配置新增警情分析数据
jqfx-mysqldb:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************************************
    username: ys
    password: 123456
    druid:
    max-wait: 10000
    validation-query: select 1
    validation-query-timeout: 200
    initial-size: 5
    max-active: 5
    min-idle: 5
```

## v1.5 发版日志

> XMKFB-856、后-热门模型相关接口开发  
> XMKFB-814、后-系统使用情况接口修改  
> XMKFB-853、后-个人排行榜接口提供单位能效榜接口提供  
> XMKFB-943、重复报警接口提供  
> XMKFB-1002[XMKFB-990] 后-出警相关接口提供  
> XMKFB-1001[XMKFB-990] 后-警情相关接口提供

## v1.3 发版日志

> XMKFB-699、合-数据列表检索优化   
> XMKFB-627、合-统一门户日志优化   
> XMKFB-647、【统一门户】新增数据详情页面

## v1.3 数据库更新

```sql
#新增表t_portal_oper_log字段third_party_name的索引
ALTER TABLE `t_portal_oper_log`
    ADD INDEX `third_party_name_idx`(`third_party_name`) USING BTREE;
```

## v1.0 发版日志

# 2023-12-04

> XMKFB-148、后-领导首页-智能应用开发
> XMKFB-143、后-日志管理开发

# 2023-12-05

> XMKFB-146、登录相关开发

# 2023-12-06

> XMKFB-144、后-用户管理开发

# 2023-12-07

> XMKFB-145、后-消息中心开发
> XMKFB-151、后-赋能统计接口开发

# 2023-12-08

> XMKFB-149、后-系统使用情况开发  
> XMKFB-150、后-接警处警统计、案件统计开发

# 2023-12-11

> XMKFB-147、后-民警首页开发

# 2023-12-12

> XMKFB-150、后-接警处警统计，案件统计开发

# 2023-12-13

> XMKFB-183、后-消息中心检索下拉框接口提供

# 2023-12-20

> XMKFB-201、后-用户需要定时从第三方进行同步

# 2023-12-22

> XMKFB-196、后-云哨消息对接门户

# 2023-12-27

> XMKFB-258、日志列表优化-按操作时间倒排  
> XMKFB-274、只能应用添加排序字段

# 2024-01-04

> XMKFB-301、高新-【统一门户】后台管理权限设置优化

# nacos配置更新

```
#minio配置
com:
  trs:
    minio:
      access-key: admin
      secret-key: minioadmin
      host: http://************:9000/
      url: http://************:9000/
      bucket: ys-dev
    #minio暴露的互联网地址
    internetAddress: https://ys.dev.trs/oss/file/
    #盯办转发地址，正式环境需替换成:http://***********:81/mosty-api
    forward:
      dingBanForwardUrl: http://************:8006/mosty-api
    #高新区单位的code集合
    jjcj:
      gxDwCodes: 510109470000,510109540000,510109530000,510109510000,510109500000,510109490000,510109480000,510109450000,510109430000,510109410000,510109400000
    #第三方用户同步定时任务cron表达式，默认每五分钟执行一次
    schedule:
      syncUser: 0 0/5 * * * ?
#XMKFB-196 增加统一门户nacos配置
spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ************:9092
          consumer-properties:
            enable-auto-commit: true
            group-id: portal_group
            auto-offset-reset: latest
      function:
        definition: portalMessageTopic
      bindings:
        portalMessageTopic-in-0:
          destination: portal_message
          group: portal_group
          contentType: application/json
#XMKFB-196 增加云哨消息模块nacos配置，推送消息的topic和是否开启消息推送门户，默认false
message:
    portal:
        topic: portal_message
        sync: true
        bootstrap-servers: ************:9092
        producer:
            key-serializer: org.apache.kafka.common.serialization.StringSerializer
            value-serializer: org.apache.kafka.common.serialization.StringSerializer
#XMKFB-194 增加日志模块nacos配置，是否开启日志推送统一门户，默认false
log:
    portal:
        sync: true
```