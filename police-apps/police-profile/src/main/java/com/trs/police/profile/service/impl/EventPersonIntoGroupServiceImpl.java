package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.trs.common.utils.TimeUtils;
import com.trs.police.common.core.utils.StringUtil;
import com.trs.police.profile.converter.EventPersonIntoGroupConverter;
import com.trs.police.profile.domain.entity.EventGroupRelation;
import com.trs.police.profile.domain.entity.EventPersonRelation;
import com.trs.police.profile.domain.entity.PersonGroupRelation;
import com.trs.police.profile.domain.vo.EventGroupPersonSynVO;
import com.trs.police.profile.mapper.EventGroupRelationMapper;
import com.trs.police.profile.mapper.EventPersonRelationMapper;
import com.trs.police.profile.mapper.PersonGroupRelationMapper;
import com.trs.police.profile.service.EventPersonIntoGroupService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Description: 事件档案关联人员数据同步关联到群体档案
 *
 * @author: gao.yuan
 * @create: 2024-10-10 18:44
 */
@Service
@Slf4j
public class EventPersonIntoGroupServiceImpl implements EventPersonIntoGroupService {

    @Autowired
    private EventPersonRelationMapper eventPersonRelationMapper;

    @Autowired
    private EventGroupRelationMapper eventGroupRelationMapper;

    @Autowired
    private PersonGroupRelationMapper personGroupRelationMapper;

    @Autowired
    private EventPersonIntoGroupConverter eventPersonIntoGroupConverter;

    /**
     * 同步事件档案关联人员数据到群体档案
     */
    @Override
    public void synEventPersonToGroup() {
        // 捞取新增了群体的事件id集合
        List<Long> extEventId = getEventIdByAddGroup();
        QueryWrapper<EventPersonRelation> wrapper = buildQueryWrapper(extEventId);
        List<EventPersonRelation> relations = eventPersonRelationMapper.selectList(wrapper);
        List<List<EventPersonRelation>> partition = Lists.partition(relations, 500);
        log.info("事件档案关联人员数据同步关联到群体档案定时任务同步数据量{}，总页数{}", relations.size(), partition.size());
        AtomicInteger page = new AtomicInteger(0);
        for (List<EventPersonRelation> personRelations : partition) {
            page.getAndIncrement();
            // 事件关联的人员map
            Map<Long, List<Long>> eventIdAndPersonIdMap = personRelations.stream()
                    .collect(Collectors.groupingBy(EventPersonRelation::getEventId,
                            Collectors.collectingAndThen(Collectors.toList(),
                                    list -> list.stream().map(EventPersonRelation::getPersonId)
                                            .collect(Collectors.toList()))));
            // 事件下群组关联的人员map
            List<EventGroupPersonSynVO> eventGroupPersonRelations = eventGroupRelationMapper.selectGroupPersonIdByEventId(eventIdAndPersonIdMap.keySet());
            Map<Long, Map<Long, List<Long>>> eventIdAndGroupIdAndPersonIdMap = eventGroupPersonRelations.stream().collect(Collectors.groupingBy(EventGroupPersonSynVO::getEventId,
                    Collectors.collectingAndThen(Collectors.toList(),
                            list -> list.stream().collect(Collectors.groupingBy(EventGroupPersonSynVO::getGroupId,
                                    Collectors.collectingAndThen(Collectors.toList(),
                                            list1 -> list1.stream().map(EventGroupPersonSynVO::getPersonId).collect(Collectors.toList())))))));
            if (CollectionUtils.isEmpty(eventGroupPersonRelations)) {
                // 如果所有事件都没有关联群体
                log.info("事件档案关联人员数据同步关联到群体档案定时任务，第{}页所有事件都没有关联群体，循环跳过", page.get());
                continue;
            }
            // 群组下关联人员map，用于多事件关联统一群体，且人员重复时排重
            Map<Long, List<Long>> initGroupPersonIdMap = eventGroupPersonRelations.stream().collect(Collectors.groupingBy(EventGroupPersonSynVO::getGroupId,
                    Collectors.collectingAndThen(Collectors.toList(),
                            list -> list.stream().map(EventGroupPersonSynVO::getPersonId)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()))));
            // 对关联了群组的事件id进行循环，并补充当前事件的关联人员数据
            eventIdAndGroupIdAndPersonIdMap.keySet()
                    .forEach(eventId -> saveRelation(eventIdAndPersonIdMap.get(eventId),
                            eventIdAndGroupIdAndPersonIdMap.get(eventId),
                            initGroupPersonIdMap));
            log.info("事件档案关联人员数据同步关联到群体档案定时任务，第{}页执行完成", page.get());
        }
    }

    private List<Long> getEventIdByAddGroup() {
        String beforeDay = BeanFactoryHolder.getEnv().getProperty("profile.event.personIntoGroup.groupBeforeDay");
        QueryWrapper<EventGroupRelation> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("group_id");
        if (!StringUtil.isEmpty(beforeDay)) {
            String startTime = TimeUtils.dateBefOrAft(-Integer.parseInt(beforeDay), TimeUtils.YYYYMMDD_HHMMSS);
            wrapper.ge("update_time", startTime);
        }
        wrapper.select("event_id");
        return eventGroupRelationMapper.selectList(wrapper)
                .stream()
                .map(EventGroupRelation::getEventId)
                .distinct()
                .collect(Collectors.toList());
    }

    private void saveRelation(List<Long> personIds, Map<Long, List<Long>> groupIdAndPersonIdMap, Map<Long, List<Long>> initGroupPersonIdMap) {
        groupIdAndPersonIdMap.forEach((groupId, existPersonId) -> {
            List<Long> insertPersonId = null;
            if (!CollectionUtils.isEmpty(existPersonId)) {
                insertPersonId = personIds.stream()
                        .filter(personId -> !existPersonId.contains(personId))
                        .filter(personId -> !initGroupPersonIdMap.get(groupId).contains(personId))
                        .distinct()
                        .collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(insertPersonId)) {
                insertPersonId.forEach(personId -> {
                    PersonGroupRelation personGroupRelation = eventPersonIntoGroupConverter.groupIdAndPersonIdToPersonGroupRelation(groupId, personId);
                    personGroupRelationMapper.insert(personGroupRelation);
                });
                initGroupPersonIdMap.get(groupId).addAll(insertPersonId);
            }
        });
    }

    private QueryWrapper<EventPersonRelation> buildQueryWrapper(List<Long> extEventId) {
        // 第一次同步全部数据，后续同步新增数据
        String beforeDay = BeanFactoryHolder.getEnv().getProperty("profile.event.personIntoGroup.beforeDay");
        QueryWrapper<EventPersonRelation> wrapper = new QueryWrapper<>();
        wrapper.isNotNull("person_id");
        if (!StringUtil.isEmpty(beforeDay)) {
            String startTime = TimeUtils.dateBefOrAft(-Integer.parseInt(beforeDay), TimeUtils.YYYYMMDD_HHMMSS);
            wrapper.ge("update_time", startTime);
        }
        if (!CollectionUtils.isEmpty(extEventId)) {
            wrapper.or().in("event_id", extEventId);
        }
        wrapper.select("event_id", "person_id");
        return wrapper;
    }
}
