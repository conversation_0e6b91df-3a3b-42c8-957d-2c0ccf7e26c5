
package com.trs.police.profile.service.zhzg.statistic;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.constant.enums.PoliceRankEnum;
import com.trs.police.profile.constant.enums.PoliceZjqkEnum;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * xx
 */
@Service
public class ZjqkStatistic implements ProfileStatisticScene<CountItem, HzBigScreenDto> {

    @Autowired
    private DictService dictService;

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Override
    public List<CountItem> search(HzBigScreenDto dto) {
        if (StringUtils.isNotEmpty(dto.getAreaCode())){
            dto.setAreaCode(AreaUtils.areaPrefix(dto.getAreaCode()));
        }
        List<Long> codes = PoliceZjqkEnum.typeOf(dto.getType(), dto.getZjType());
        List<CountItem> resultList = profilePoliceMapper.zjqkStatistic(dto,codes);
        Map<String, Long> resultMap = CollectionUtils.isEmpty(resultList) ? new HashMap<>()
                : resultList.stream().collect(Collectors.toMap(CountItem::getKey, CountItem::getCount));
        List<CountItem> list = new ArrayList<>();
        for (Long code : codes) {
            CountItem countItem = new CountItem();
            countItem.setCount(resultMap.getOrDefault(String.valueOf(code),0L));
            countItem.setKey(String.valueOf(code));
            countItem.setName(PoliceRankEnum.codeOf(Integer.valueOf(code.intValue())));
            countItem.setShowName(countItem.getName());
            list.add(countItem);
        }
        return list;
    }

    @Override
    public String key() {
        return "zjqkStatistic";
    }

    @Override
    public String desc() {
        return "职级情况统计";
    }
}
