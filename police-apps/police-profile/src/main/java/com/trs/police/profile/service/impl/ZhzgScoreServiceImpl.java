package com.trs.police.profile.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.common.core.dto.DictDto;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.converter.ZhzgScoreConverter;
import com.trs.police.profile.domain.dto.zhzg.*;
import com.trs.police.profile.domain.entity.Dict;
import com.trs.police.profile.domain.entity.zhzg.*;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import com.trs.police.profile.mapper.DictMapper;
import com.trs.police.profile.mapper.zhzg.*;
import com.trs.police.profile.service.zhzg.ZhzgScoreCalculateService;
import com.trs.police.profile.service.zhzg.ZhzgScoreService;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import com.trs.web.builder.base.RestfulResultsV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 积分规则服务实现
 */

@Slf4j
@Service
public class ZhzgScoreServiceImpl implements ZhzgScoreService {

    @Resource
    private ZhzgScoreCalculateService calculateService;

    @Resource
    private ZhzgScoreRuleMapper zhzgScoreRuleMapper;

    @Resource
    private ProfilePoliceMapper profilePoliceMapper;

    @Resource
    private ZhzgScoreConverter converter;

    @Resource
    private PoliceRankRelationMapper rankMapper;

    @Resource
    private PoliceResumeRelationMapper resumeMapper;

    @Resource
    private PoliceAssistTibetXinjiangRelationMapper tibetXinjiangMapper;

    @Resource
    private PoliceEducationExperienceRelationMapper educationMapper;

    @Resource
    private PoliceFamilyRelationMapper familyMapper;

    @Resource
    private PoliceProfessionalTechnologyRelationMapper technologyMapper;

    @Resource
    private PoliceProfessionalLgsjRelationMapper lgsjMapper;

    @Resource
    private PoliceProfessionalWgwjRelationMapper wgwjMapper;

    @Resource
    private PoliceProfessionalNdkhRelationMapper ndkhMapper;

    @Resource
    private PoliceDemocraticEvaluationRelationMapper democraticEvaluationMapper;

    @Resource
    private DictMapper dictMapper;

    @Resource
    private DictService dictService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createScoreRule(CreateZhzgScoreRuleRequestDTO request) {
        ZhzgScoreRuleEntity entity = new ZhzgScoreRuleEntity();
        if (request.getId() != null) {
            entity = zhzgScoreRuleMapper.selectById(request.getId());
        }
        BeanUtils.copyProperties(request, entity);
        if (request.getId() != null) {
            zhzgScoreRuleMapper.updateById(entity);
        } else {
            // 新增规则，设置默认启用状态
            entity.setIsEnabled(true);
            zhzgScoreRuleMapper.insert(entity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteScoreRule(Long id) {
        if (zhzgScoreRuleMapper.selectCount(Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getParentId, id)) > 0) {
            throw new RuntimeException("存在子规则，无法删除");
        }
        zhzgScoreRuleMapper.deleteById(id);
    }

    @Override
    public List<GetZhzgScoreRuleTreeNonLeafDTO> getNonLeafScoreTree() {
        // 查询所有非叶子节点
        List<ZhzgScoreRuleEntity> ruleEntities = zhzgScoreRuleMapper.selectList(Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getIsLeaf, false)
                .orderByAsc(ZhzgScoreRuleEntity::getId));

        // 转换为DTO
        List<GetZhzgScoreRuleTreeNonLeafDTO> dtoList = ruleEntities.stream()
                .map(this::convertToNonLeafDTO)
                .collect(java.util.stream.Collectors.toList());

        // 使用泛型方法构建树形结构
        return com.trs.police.profile.util.zhzg.ZhzgScoreUtil.buildTree(dtoList);
    }

    /**
     * 将实体转换为非叶子节点DTO
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    private GetZhzgScoreRuleTreeNonLeafDTO convertToNonLeafDTO(ZhzgScoreRuleEntity entity) {
        GetZhzgScoreRuleTreeNonLeafDTO dto = new GetZhzgScoreRuleTreeNonLeafDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setFullScore(entity.getFullScore());
        dto.setParentId(entity.getParentId());
        return dto;
    }

    @Override
    public RestfulResultsV2<GetZhzgScoreRuleTreeLeafDTO> getLeafScoreTree(Long parentId,
                                                                          PageParams pageParams,
                                                                          Integer applicableRank,
                                                                          String type,
                                                                          String value) {
        List<Integer> rankList = new ArrayList<>();
        if (Objects.nonNull(applicableRank)) {
            DictDto p = dictService.getDictByTypeAndCode("police_zj", applicableRank.longValue());
            if (Objects.nonNull(p) && !p.getChildren().isEmpty()) {
                rankList = p.getChildren().stream().map(DictDto::getCode).map(Long::intValue).collect(Collectors.toList());
            } else {
                rankList.add(applicableRank);
            }
        }


        Page<ZhzgScoreRuleEntity> page = zhzgScoreRuleMapper.selectPage(pageParams.toPage(), Wrappers.lambdaQuery(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getIsLeaf, true)
                .eq(ZhzgScoreRuleEntity::getParentId, parentId)
                .in(Objects.nonNull(applicableRank), ZhzgScoreRuleEntity::getApplicableRank, rankList)
                .like("name".equals(type) || "all".equals(type), ZhzgScoreRuleEntity::getName, value)
                .like("description".equals(type) || "all".equals(type), ZhzgScoreRuleEntity::getDescription, value));
        List<GetZhzgScoreRuleTreeLeafDTO> result = page.getRecords()
                .stream()
                .map(entity -> {
                    GetZhzgScoreRuleTreeLeafDTO dto = new GetZhzgScoreRuleTreeLeafDTO();
                    BeanUtils.copyProperties(entity, dto);
                    dictMapper.selectList(Wrappers.<Dict>lambdaQuery()
                                    .eq(Dict::getCode, entity.getApplicableRank())
                                    .likeRight(Dict::getType, "police_zj"))
                            .stream().findFirst().ifPresent(dict -> dto.setApplicableRankName(dict.getName()));
                    return dto;
                })
                .collect(Collectors.toList());
        return RestfulResultsV2.ok(result)
                .addPageNum(pageParams.getPageNumber())
                .addPageSize(pageParams.getPageSize())
                .addTotalCount(page.getTotal());
    }

    @Override
    public void changeScoreRuleEnabledStatus(Long id, Boolean enabled) {
        zhzgScoreRuleMapper.update(null, Wrappers.lambdaUpdate(ZhzgScoreRuleEntity.class)
                .eq(ZhzgScoreRuleEntity::getId, id)
                .set(ZhzgScoreRuleEntity::getIsEnabled, enabled));
    }

    @Override
    public void calculate() {
        calculateScore();
    }

    @Override
    public JsonNode getScoreDetail(Long personId) {
        ProfilePolice profilePolice = profilePoliceMapper.selectById(personId);
        return JsonUtil.parseJsonNode(profilePolice.getScoreDetail());
    }

    /**
     * 定时计算积分，每天0点0分0秒执行
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void calculateScore() {
        log.info("开始积分计算。");
        List<ZhzgPersonArchiveDTO> archives = profilePoliceMapper.selectList(Wrappers.<ProfilePolice>lambdaQuery()
                        .eq(ProfilePolice::getDeleted, false))
                .stream()
                .map(this::buildPersonArchive)
                .collect(Collectors.toList());
        log.info("积分计算初始化数据：获取到{}个人员", archives.size());
        List<ZhzgScoreRuleDTO> ruleList = zhzgScoreRuleMapper.selectList(Wrappers.<ZhzgScoreRuleEntity>lambdaQuery()
                        .eq(ZhzgScoreRuleEntity::getIsEnabled, true))
                .stream()
                .map(converter::toRuleDTO)
                .collect(Collectors.toList());

        List<ZhzgScoreRuleDTO> rules = ZhzgScoreUtil.buildRuleTree(ruleList);
        log.info("积分计算初始化数据：获取到{}条规则", ruleList.size());

        for (ZhzgPersonArchiveDTO archive : archives) {
            log.info("开始{}积分更新", archive.getName());
            ZhzgScoreResultVO zhzgScoreResultVO = calculateService.calculateScore(archive, rules);
            List<ZhzgRuleScoreDetailVO> ruleScoreDetails = zhzgScoreResultVO.getRuleScoreDetails();

            // 将ruleScoreDetails转换为树状结构
            List<ZhzgRuleScoreTreeNodeDTO> treeNodes = convertToTreeNodes(ruleScoreDetails);
            List<ZhzgRuleScoreTreeNodeDTO> tree = ZhzgScoreUtil.buildTree(treeNodes);

            // 将树状结构序列化为JSON字符串
            String treeJson = JsonUtil.toJsonString(tree);
            log.info("{}的计算说明:{}", archive.getName(), zhzgScoreResultVO.getDescription());
            for (int i = 0; i < ruleScoreDetails.size(); i++) {
                log.info("  {}. {}", (i + 1), ruleScoreDetails.get(i).getCalculateDescription());
            }
            profilePoliceMapper.update(null, Wrappers.<ProfilePolice>lambdaUpdate()
                    .eq(ProfilePolice::getId, archive.getId())
                    .set(ProfilePolice::getScore, zhzgScoreResultVO.getTotalScore())
                    .set(ProfilePolice::getScoreDetail, treeJson));
            log.info("{}积分更新完成。", archive.getName());
        }
        log.info("积分计算任务完成。");
    }

    /**
     * 将ZhzgRuleScoreDetailVO列表转换为ZhzgRuleScoreTreeNodeDTO列表
     *
     * @param ruleScoreDetails 规则积分详情列表
     * @return 树节点列表
     */
    private List<ZhzgRuleScoreTreeNodeDTO> convertToTreeNodes(List<ZhzgRuleScoreDetailVO> ruleScoreDetails) {
        if (ruleScoreDetails == null || ruleScoreDetails.isEmpty()) {
            return new ArrayList<>();
        }

        return ruleScoreDetails.stream()
                .map(detail -> ZhzgRuleScoreTreeNodeDTO.builder()
                        .ruleId(detail.getRuleId())
                        .ruleName(detail.getRuleName())
                        .parentRuleId(detail.getParentRuleId())
                        .score(detail.getScore())
                        .children(new ArrayList<>())
                        .build())
                .collect(Collectors.toList());
    }

    private ZhzgPersonArchiveDTO buildPersonArchive(ProfilePolice profilePolice) {
        ZhzgPersonArchiveDTO archive = converter.toArchive(profilePolice);
        archive.setAssessments(ndkhMapper.selectList(Wrappers.<PoliceProfessionalNdkhRelation>lambdaQuery()
                        .eq(PoliceProfessionalNdkhRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceProfessionalNdkhRelation::getDeleted, false))
                .stream()
                .map(converter::toNdkhDTO)
                .collect(Collectors.toList()));

        archive.setViolations(wgwjMapper.selectList(Wrappers.<PoliceProfessionalWgwjRelation>lambdaQuery()
                        .eq(PoliceProfessionalWgwjRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceProfessionalWgwjRelation::getDeleted, false))
                .stream()
                .map(converter::toWgwjDTO)
                .collect(Collectors.toList()));

        archive.setAwards(lgsjMapper.selectList(Wrappers.<PoliceProfessionalLgsjRelation>lambdaQuery()
                        .eq(PoliceProfessionalLgsjRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceProfessionalLgsjRelation::getDeleted, false))
                .stream()
                .map(converter::toLgsjDTO)
                .collect(Collectors.toList()));

        archive.setExpertises(technologyMapper.selectList(Wrappers.<PoliceProfessionalTechnologyRelation>lambdaQuery()
                        .eq(PoliceProfessionalTechnologyRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceProfessionalTechnologyRelation::getDeleted, false))
                .stream()
                .map(converter::toExpertisesDTO)
                .collect(Collectors.toList()));

        archive.setRanks(rankMapper.selectList(Wrappers.<PoliceRankRelation>lambdaQuery()
                        .eq(PoliceRankRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceRankRelation::getDeleted, false))
                .stream()
                .map(converter::toRankDTO)
                .collect(Collectors.toList()));

        archive.setResumes(resumeMapper.selectList(Wrappers.<PoliceResumeRelation>lambdaQuery()
                        .eq(PoliceResumeRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceResumeRelation::getDeleted, false))
                .stream()
                .map(converter::toResumeDTO)
                .collect(Collectors.toList()));

        archive.setSupports(tibetXinjiangMapper.selectList(Wrappers.<PoliceAssistTibetXinjiangRelation>lambdaQuery()
                        .eq(PoliceAssistTibetXinjiangRelation::getProfileId, profilePolice.getId())
                        .eq(PoliceAssistTibetXinjiangRelation::getDeleted, false))
                .stream()
                .map(converter::toSupportDTO)
                .collect(Collectors.toList()));
        return archive;
    }
}
