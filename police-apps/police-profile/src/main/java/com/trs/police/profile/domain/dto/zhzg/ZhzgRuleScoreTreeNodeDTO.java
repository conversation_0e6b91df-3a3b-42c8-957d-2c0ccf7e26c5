package com.trs.police.profile.domain.dto.zhzg;

import com.trs.police.profile.util.zhzg.TreeNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智慧政工规则积分树节点DTO
 * 用于构建简化的树状结构，只包含必要的字段
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhzgRuleScoreTreeNodeDTO implements TreeNode<ZhzgRuleScoreTreeNodeDTO, Long> {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 父规则ID
     */
    private Long parentRuleId;

    /**
     * 获得的积分
     */
    private Double score;

    /**
     * 子节点列表
     */
    private List<ZhzgRuleScoreTreeNodeDTO> children;

    @Override
    public Long getId() {
        return ruleId;
    }

    @Override
    public Long getParentId() {
        return parentRuleId;
    }

    @Override
    public List<ZhzgRuleScoreTreeNodeDTO> getChildren() {
        return children;
    }

    @Override
    public void setChildren(List<ZhzgRuleScoreTreeNodeDTO> children) {
        this.children = children;
    }
}
