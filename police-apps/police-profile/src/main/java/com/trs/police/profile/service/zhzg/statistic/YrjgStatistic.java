
package com.trs.police.profile.service.zhzg.statistic;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.openfeign.starter.service.DictService;
import com.trs.police.profile.domain.dto.zhzg.HzBigScreenDto;
import com.trs.police.profile.mapper.zhzg.ProfilePoliceMapper;
import com.trs.police.profile.service.secne.ProfileStatisticScene;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * xx
 */
@Service
public class YrjgStatistic implements ProfileStatisticScene<CountItem, HzBigScreenDto> {

    @Autowired
    private DictService dictService;

    @Autowired
    private ProfilePoliceMapper profilePoliceMapper;

    @Override
    public List<CountItem> search(HzBigScreenDto hzBigScreenDto) {
        String count = BeanFactoryHolder.getEnv().getProperty("profile.person.yrjg","{\"sg\":32, \"bmzz\":32, \"zkj\":32, \"fkj\":32,\"mj\":32,\"sanjzr\":32,\"sjzr\":32}");
        HashMap<String,Long> hashMap = JSONObject.parseObject(count, HashMap.class);
        CountItem rmjcItem = new CountItem();
        rmjcItem.setKey("rmjcCount");
        rmjcItem.setName("人民警察");
        rmjcItem.setShowName("人民警察");
        rmjcItem.setCount(1202L);
        List<CountItem> list = new ArrayList<>();
        list.add(rmjcItem);

        CountItem sydwItem = new CountItem();
        sydwItem.setKey("sydwCount");
        sydwItem.setName("事业单位");
        sydwItem.setShowName("事业单位");
        sydwItem.setCount(1145L);
        list.add(sydwItem);

        CountItem jggqItem = new CountItem();
        jggqItem.setKey("jggqCount");
        jggqItem.setName("机关工勤");
        jggqItem.setShowName("机关工勤");
        jggqItem.setCount(1015L);
        list.add(jggqItem);

        CountItem jwfzItem = new CountItem();
        jwfzItem.setKey("jwfzCount");
        jwfzItem.setName("警务辅助");
        jwfzItem.setShowName("警务辅助");
        jwfzItem.setCount(802L);
        list.add(jwfzItem);

        CountItem qtItem = new CountItem();
        qtItem.setKey("qtCount");
        qtItem.setName("其他人员");
        qtItem.setShowName("其他人员");
        qtItem.setCount(725L);
        list.add(qtItem);

        return list;
    }

    @Override
    public String key() {
        return "yrjgStatistic";
    }

    @Override
    public String desc() {
        return "人员结构统计";
    }
}
