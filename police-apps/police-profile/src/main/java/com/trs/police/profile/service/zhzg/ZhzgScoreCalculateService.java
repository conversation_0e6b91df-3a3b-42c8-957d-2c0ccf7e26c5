package com.trs.police.profile.service.zhzg;

import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgScoreResultVO;
import com.trs.police.profile.factory.zhzg.ZhzgScoreStrategyFactory;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智慧政工积分计算服务
 */
@Slf4j
@Service
public class ZhzgScoreCalculateService {

    @Autowired
    private ZhzgScoreStrategyFactory strategyFactory;

    /**
     * 计算积分
     *
     * @param personArchive 档案信息
     * @param rules 积分规则
     * @return 积分计算结果
     */
    public ZhzgScoreResultVO calculateScore(ZhzgPersonArchiveDTO personArchive, List<ZhzgScoreRuleDTO> rules) {
        log.info("开始计算人员{}积分", personArchive.getName());

        ZhzgScoreResultVO.ZhzgScoreResultVOBuilder builder = ZhzgScoreResultVO.builder()
                .personId(personArchive.getId())
                .personName(personArchive.getName())
                .calculateTime(LocalDateTime.now())
                .success(true)
                .ruleScoreDetails(new ArrayList<>());

        try {
            // 验证请求数据
            if (!validateRequest(personArchive, rules)) {
                return builder
                        .totalScore(0.0)
                        .success(false)
                        .errorMessage("请求数据验证失败")
                        .build();
            }

            // 计算总积分
            ScoreCalculationResult result = calculateTotalScore(personArchive, rules);
            log.info("计算人员{}积分完成", personArchive.getName());
            return builder
                    .totalScore(result.getTotalScore())
                    .ruleScoreDetails(result.getDetails())
                    .description(String.format("共计算%d个规则，总得分%.1f分", result.getDetails().size(), result.getTotalScore()))
                    .build();

        } catch (Exception e) {
            log.error("计算智慧政工积分失败，人员：{}，错误：{}",
                    personArchive.getName(), e.getMessage(), e);
            return builder
                    .totalScore(0.0)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    /**
     * 计算总积分（递归处理树形结构）
     *
     * @param personArchive 人员档案
     * @param rules         规则列表
     * @return 计算结果
     */
    private ScoreCalculationResult calculateTotalScore(ZhzgPersonArchiveDTO personArchive, List<ZhzgScoreRuleDTO> rules) {
        ScoreCalculationResult result = new ScoreCalculationResult();

        for (ZhzgScoreRuleDTO rule : rules) {
            ScoreCalculationResult ruleResult = calculateRuleScore(personArchive, rule);
            result.addScore(ruleResult.getTotalScore());
            result.addDetails(ruleResult.getDetails());
        }

        return result;
    }

    /**
     * 计算单个规则的积分
     *
     * @param personArchive 人员档案
     * @param rule          积分规则
     * @return 计算结果
     */
    private ScoreCalculationResult calculateRuleScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        ScoreCalculationResult result = new ScoreCalculationResult();

        if (rule.getIsLeaf() != null && rule.getIsLeaf()) {
            // 叶子节点，执行具体的积分计算策略
            ZhzgRuleScoreDetailVO detail = executeScoreStrategy(personArchive, rule);
            if (detail != null) {
                result.addScore(detail.getScore());
                result.addDetail(detail);
            }
        } else {
            // 非叶子节点，递归计算所有子节点的积分
            if (!CollectionUtils.isEmpty(rule.getChildren())) {
                ScoreCalculationResult childrenResult = calculateTotalScore(personArchive, rule.getChildren());
                
                // 应用父节点的积分上限
                double childrenTotalScore = childrenResult.getTotalScore();
                double finalScore = childrenTotalScore;
                
                if (rule.getFullScore() != null && rule.getFullScore() > 0 && childrenTotalScore > rule.getFullScore()) {
                    finalScore = rule.getFullScore();
                    log.debug("父规则积分超出上限，规则：{}，子节点总分：{}，上限：{}，最终得分：{}", 
                            rule.getName(), childrenTotalScore, rule.getScore(), finalScore);
                }
                
                result.addScore(finalScore);
                result.addDetails(childrenResult.getDetails());
                
                // 添加父规则的汇总记录
                ZhzgRuleScoreDetailVO parentDetail = ZhzgRuleScoreDetailVO.builder()
                        .ruleId(rule.getId())
                        .ruleName(rule.getName())
                        .ruleDescription(rule.getDescription())
                        .ruleType(rule.getRuleType())
                        .isLeaf(false)
                        .parentRuleId(rule.getParentId())
                        .score(finalScore)
                        .maxScore(rule.getFullScore())
                        .isHit(finalScore > 0)
                        .calculateDescription(String.format("%s子规则总分：%.1f分%s",
                                rule.getName(),
                                childrenTotalScore, 
                                finalScore != childrenTotalScore ? "，受上限限制，最终得分：" + finalScore + "分" : ""))
                        .success(true)
                        .build();
                result.addDetail(parentDetail);
            }
        }

        return result;
    }

    /**
     * 执行具体的积分计算策略
     *
     * @param personArchive 人员档案
     * @param rule          积分规则
     * @return 积分详情
     */
    private ZhzgRuleScoreDetailVO executeScoreStrategy(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        ZhzgScoreStrategy strategy = strategyFactory.getStrategy(rule.getName(), rule.getRuleType());
        
        if (strategy == null) {
            log.debug("未找到对应的积分策略，规则：{}，类型：{}", rule.getName(), rule.getRuleType());
            return ZhzgRuleScoreDetailVO.builder()
                    .ruleId(rule.getId())
                    .ruleName(rule.getName())
                    .ruleDescription(rule.getDescription())
                    .ruleType(rule.getRuleType())
                    .isLeaf(true)
                    .parentRuleId(rule.getParentId())
                    .score(0.0)
                    .maxScore(rule.getFullScore())
                    .isHit(false)
                    .success(false)
                    .errorMessage("未找到对应的积分策略")
                    .build();
        }

        log.debug("执行积分策略，规则：{}，策略：{}", rule.getName(), strategy.getStrategyName());
        return strategy.calculateScore(personArchive, rule);
    }

    /**
     * 构建规则树
     *
     * @param rules 规则列表
     * @return 规则树
     */
    private List<ZhzgScoreRuleDTO> buildRuleTree(List<ZhzgScoreRuleDTO> rules) {
        Map<Long, ZhzgScoreRuleDTO> ruleMap = new HashMap<>();
        List<ZhzgScoreRuleDTO> rootRules = new ArrayList<>();

        // 构建规则映射
        for (ZhzgScoreRuleDTO rule : rules) {
            ruleMap.put(rule.getId(), rule);
            rule.setChildren(new ArrayList<>());
        }

        // 构建树形结构
        for (ZhzgScoreRuleDTO rule : rules) {
            if (rule.getParentId() == null || rule.getParentId() == 0) {
                rootRules.add(rule);
            } else {
                ZhzgScoreRuleDTO parent = ruleMap.get(rule.getParentId());
                if (parent != null) {
                    parent.getChildren().add(rule);
                }
            }
        }

        return rootRules;
    }

    /**
     * 验证请求数据
     *
     * @param personArchive 档案信息
     * @param rules 积分规则
     * @return 是否有效
     */
    private boolean validateRequest(ZhzgPersonArchiveDTO personArchive, List<ZhzgScoreRuleDTO> rules) {
        return personArchive != null
                && personArchive.getId() != null
                && !CollectionUtils.isEmpty(rules);
    }

    /**
     * 积分计算结果内部类
     */
    private static class ScoreCalculationResult {
        private double totalScore = 0.0;
        private List<ZhzgRuleScoreDetailVO> details = new ArrayList<>();

        public void addScore(Double score) {
            if (score != null) {
                this.totalScore += score;
            }
        }

        public void addDetail(ZhzgRuleScoreDetailVO detail) {
            if (detail != null) {
                this.details.add(detail);
            }
        }

        public void addDetails(List<ZhzgRuleScoreDetailVO> details) {
            if (details != null) {
                this.details.addAll(details);
            }
        }

        public double getTotalScore() {
            return totalScore;
        }

        public List<ZhzgRuleScoreDetailVO> getDetails() {
            return details;
        }
    }

}
