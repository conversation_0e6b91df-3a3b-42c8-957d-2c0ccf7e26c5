package com.trs.police.profile.service.zhzg;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.profile.domain.dto.zhzg.CreateZhzgScoreRuleRequestDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeLeafDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeNonLeafDTO;
import com.trs.web.builder.base.RestfulResultsV2;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 积分规则服务
 */

public interface ZhzgScoreService {

    /**
     * 创建积分规则
     *
     * @param request 创建积分规则请求
     */
    void createScoreRule(CreateZhzgScoreRuleRequestDTO request);

    /**
     * 删除积分规则
     *
     * @param id 积分规则id
     */
    void deleteScoreRule(@PathVariable("id") Long id);

    /**
     * 获取非叶子节点的积分规则树
     *
     * @return 规则树
     */
    List<GetZhzgScoreRuleTreeNonLeafDTO> getNonLeafScoreTree();

    /**
     * 获取叶子节点的积分规则树
     *
     * @param parentId 父节点id
     * @param pageParams 分页参数
     * @param applicableRank 适用职级
     * @param type 检索类型
     * @param value 检索值
     * @return 规则树
     */
    RestfulResultsV2<GetZhzgScoreRuleTreeLeafDTO> getLeafScoreTree(Long parentId,
                                                                   PageParams pageParams,
                                                                   Integer applicableRank,
                                                                   String type,
                                                                   String value);

    /**
     * 启用/禁用积分规则
     *
     * @param id      规则id
     * @param enabled 是否启用
     */
    void changeScoreRuleEnabledStatus(Long id, Boolean enabled);

    /**
     * 触发积分计算
     */
    void calculate();

    /**
     * 获取人员积分详情
     *
     * @param personId 人员id
     * @return 积分详情
     */
    JsonNode getScoreDetail(Long personId);
}
