package com.trs.police.profile.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.police.common.core.params.PageParams;
import com.trs.police.profile.domain.dto.zhzg.CreateZhzgScoreRuleRequestDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeLeafDTO;
import com.trs.police.profile.domain.dto.zhzg.GetZhzgScoreRuleTreeNonLeafDTO;
import com.trs.police.profile.service.zhzg.ZhzgScoreCalculateService;
import com.trs.police.profile.service.zhzg.ZhzgScoreService;
import com.trs.web.builder.base.RestfulResultsV2;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 智慧政工积分相关controller
 */
@Slf4j
@RestController
@RequestMapping("/zhzg/score")
@Api(tags = "智慧政工积分管理")
public class ZhzgScoreController {

    @Resource
    private ZhzgScoreCalculateService scoreCalculateService;

    @Resource
    private ZhzgScoreService zhzgScoreService;

    /**
     * 创建积分规则
     *
     * @param request 创建积分规则请求
     */
    @PostMapping("/rule")
    public void createScoreRule(@RequestBody CreateZhzgScoreRuleRequestDTO request) {
        zhzgScoreService.createScoreRule(request);
    }

    /**
     * 删除积分规则
     *
     * @param id 积分规则id
     */
    @DeleteMapping("/rule/{id}")
    public void deleteScoreRule(@PathVariable("id") Long id) {
        zhzgScoreService.deleteScoreRule(id);
    }

    /**
     * 获取非叶子节点的积分规则树
     *
     * @return 规则树
     */
    @GetMapping("/rule/tree/non-leaf")
    public List<GetZhzgScoreRuleTreeNonLeafDTO> getNonLeafScoreTree() {
        return zhzgScoreService.getNonLeafScoreTree();
    }

    /**
     * 获取叶子节点的积分规则树
     *
     * @param parentId 父节点id
     * @param pageParams 分页参数
     * @param applicableRank 适用职级
     * @param type 检索类型
     * @param value 检索值
     * @return 规则树
     */
    @GetMapping("/rule/tree/leaf/{parentId}")
    public RestfulResultsV2<GetZhzgScoreRuleTreeLeafDTO> getLeafScoreTree(
            @PathVariable("parentId") Long parentId,
            PageParams pageParams,
            Integer applicableRank,
            String type,
            String value) {
        return zhzgScoreService.getLeafScoreTree(parentId, pageParams, applicableRank, type, value);
    }

    /**
     * 启用/禁用积分规则
     *
     * @param id      规则id
     * @param enabled 是否启用
     */
    @PutMapping("/rule/enabled/{id}")
    public void changeScoreRuleEnabledStatus(@PathVariable("id") Long id, @RequestParam("enabled") Boolean enabled) {
        zhzgScoreService.changeScoreRuleEnabledStatus(id, enabled);
    }

    /**
     * 触发积分计算
     */
    @GetMapping("/rule/calculate")
    public void calculate() {
        zhzgScoreService.calculate();
    }

    /**
     * 获取人员积分详情
     *
     * @param personId 人员id
     * @return 积分详情
     */
    @GetMapping("/detail/{personId}")
    public JsonNode getScoreDetail(@PathVariable("personId") Long personId) {
        return zhzgScoreService.getScoreDetail(personId);
    }
}
