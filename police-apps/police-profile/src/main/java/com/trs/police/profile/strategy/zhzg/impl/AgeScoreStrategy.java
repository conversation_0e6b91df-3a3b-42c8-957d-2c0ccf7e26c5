package com.trs.police.profile.strategy.zhzg.impl;

import com.trs.police.common.core.utils.DateUtil;
import com.trs.police.profile.domain.dto.zhzg.ZhzgPersonArchiveDTO;
import com.trs.police.profile.domain.dto.zhzg.ZhzgScoreRuleDTO;
import com.trs.police.profile.domain.vo.zhzg.ZhzgRuleScoreDetailVO;
import com.trs.police.profile.strategy.zhzg.ZhzgScoreStrategy;
import com.trs.police.profile.util.zhzg.ZhzgScoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 年龄分值积分计算策略
 */
@Slf4j
@Component
public class AgeScoreStrategy implements ZhzgScoreStrategy {

    private static final String STRATEGY_NAME = "年龄分值";
    private static final String RULE_TYPE = "AGE";

    @Override
    public ZhzgRuleScoreDetailVO calculateScore(ZhzgPersonArchiveDTO personArchive, ZhzgScoreRuleDTO rule) {
        log.debug("开始计算{}积分，人员：{}，规则：{}", STRATEGY_NAME, personArchive.getName(), rule.getName());

        ZhzgRuleScoreDetailVO.ZhzgRuleScoreDetailVOBuilder builder = ZhzgRuleScoreDetailVO.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getName())
                .ruleDescription(rule.getDescription())
                .ruleType(rule.getRuleType())
                .isLeaf(rule.getIsLeaf())
                .parentRuleId(rule.getParentId())
                .maxScore(rule.getFullScore())
                .success(true);

        try {
            // 验证数据
            if (!validatePersonArchive(personArchive) || !validateRule(rule)) {
                return builder
                        .score(0.0)
                        .isHit(false)
                        .success(false)
                        .errorMessage("数据验证失败")
                        .build();
            }

            // 获取生日
            LocalDate birthday = personArchive.getBirthday();
            //获取性别,1:男 2:女
            Integer gender = personArchive.getGender();
            //根据birthday计算年龄
            int age = DateUtil.getAgeByBirth(birthday);

            int outMonth = 0;
            //根据gender和age计算得分，男性年满50岁，每超过1个月计0.041分；女年满45周岁，每超过1个月计0.041分。
            double calculatedScore = 0.0;
            if (gender == 1 && age >= 50) {
                outMonth = (age - 50) * 12;
                calculatedScore = outMonth * rule.getScore();
            } else if (gender == 2 && age >= 45) {
                outMonth = (age - 45) * 12;
                calculatedScore = outMonth * rule.getScore();
            }

            // 应用规则配置的最大分值限制
            double finalScore = ZhzgScoreUtil.getFinalScore(calculatedScore, rule.getFullScore());

            String hitData = String.format("性别%s，年龄%d", gender, age);
            String calculateDescription = String.format("性别%s，年龄%d，超过规定%d个月 × %.3f分/月 = %.1f分",
                    gender, age, outMonth, rule.getScore(), calculatedScore);
            
            if (finalScore != calculatedScore) {
                calculateDescription += String.format("，受规则上限限制，最终得分：%.1f分", finalScore);
            }

            return builder
                    .score(finalScore)
                    .isHit(finalScore > 0)
                    .hitData(hitData)
                    .calculateDescription(calculateDescription)
                    .build();

        } catch (Exception e) {
            log.error("计算{}积分失败，人员：{}，规则：{}，错误：{}",
                    STRATEGY_NAME, personArchive.getName(), rule.getName(), e.getMessage(), e);
            return builder
                    .score(0.0)
                    .isHit(false)
                    .success(false)
                    .errorMessage("计算失败：" + e.getMessage())
                    .build();
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public String getSupportedRuleType() {
        return RULE_TYPE;
    }

    @Override
    public Boolean isEnabled() {
        return true;
    }

    @Override
    public boolean supports(String ruleName, String ruleType) {
        return RULE_TYPE.equals(ruleType) || STRATEGY_NAME.equals(ruleName);
    }

}
