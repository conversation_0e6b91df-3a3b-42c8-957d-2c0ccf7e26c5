package com.trs.police.subject.fk.service.ts;


import com.trs.common.utils.StringUtils;
import com.trs.police.common.core.utils.AreaUtils;
import com.trs.police.common.core.utils.AuthHelper;
import com.trs.police.statistic.domain.bean.CountItem;
import com.trs.police.subject.common.mapper.DataOverviewMapper;
import com.trs.police.subject.domain.dto.FkTsDto;
import com.trs.police.subject.domain.dto.SubjectSceneContext;
import com.trs.police.subject.domain.vo.fk.FkTsTopVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 数据总览
 *
 * <AUTHOR>
 * @date 2025/05/30
 */
@Service
public class DataOverviewImpl extends AbstractClass<FkTsTopVO, FkTsDto> {
    @Autowired
    private DataOverviewMapper dataOverviewMapper;

    @Override
    public List<FkTsTopVO> search(SubjectSceneContext<FkTsDto> context) {
        FkTsDto dto = context.getDto();
        if (StringUtils.isNotEmpty(dto.getAreaCode())){
            dto.setAreaCode(AreaUtils.areaKey(dto.getAreaCode()));
        }else {
            String districtCode = AuthHelper.getNotNullUser().getDept().getDistrictCode();
            dto.setAreaCode(AreaUtils.areaKey(districtCode));
        }
        //本地管控库
        List<CountItem> fkPersonItem = dataOverviewMapper.getBdgkkItem(1,dto);
        FkTsTopVO vo = new FkTsTopVO();
        vo.setBdgkCount(CollectionUtils.isNotEmpty(fkPersonItem) ? Long.valueOf(fkPersonItem.size()) : 0L);
        //涉疆人员库
        CountItem sjPersonItem = dataOverviewMapper.getSjItem();
        vo.setSjryCount(sjPersonItem == null ? 0L : sjPersonItem.getCount());
        //群体数
        List<Long> groupIds = dataOverviewMapper.getQtIds(dto);
        groupIds = CollectionUtils.isNotEmpty(groupIds) ? groupIds.stream().collect(Collectors.toList())
                : new ArrayList<>();
        vo.setQtCount(CollectionUtils.isNotEmpty(groupIds) ? Long.valueOf(groupIds.size()) : 0L);

        if ("rygk".equals(dto.getTopType())){
            CountItem activeItem = dataOverviewMapper.getActiveCount(dto);
            vo.setActiveCount(Objects.nonNull(activeItem) ? activeItem.getCount() : 0L);
            CountItem wdlrItem = dataOverviewMapper.getWdlrCount(dto);
            vo.setWdlrCount(Objects.nonNull(wdlrItem) ? wdlrItem.getCount() : 0L);
            vo.setBdlcCount(0L);
        }else {
            //重点目标单位
            CountItem zddwItem = dataOverviewMapper.getZddwItem(dto);
            vo.setZdmbdwCount(Objects.nonNull(zddwItem) ? zddwItem.getCount() : 0L);
            //预警数
            List<CountItem> bdgkkItemList = dataOverviewMapper.getBdgkkItem(null,dto);
            List<String> idCards = CollectionUtils.isNotEmpty(bdgkkItemList) ? new ArrayList<>()
                    : bdgkkItemList.stream().map(CountItem::getKey).collect(Collectors.toList());
            dto.setKeys(idCards);
            List<CountItem> warningCountItemList = dataOverviewMapper.selectWarning(dto);
            vo.setWarningCount(CollectionUtils.isNotEmpty(warningCountItemList) ? warningCountItemList.size() : 0L);
            //线索数
            List<Long> xiansuoIds = CollectionUtils.isNotEmpty(groupIds) ? dataOverviewMapper.getXsId(groupIds,dto)
                    : new ArrayList<>();
            vo.setClueCount(CollectionUtils.isNotEmpty(xiansuoIds) ? Long.valueOf(xiansuoIds.size()) : 0L);
        }

        return Arrays.asList(vo);
    }

    @Override
    public String key() {
        return "dataOverview";
    }

    @Override
    public String desc() {
        return "数据总览";
    }
}
