<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.police.statistic.mapper.ArchiveStatisticMapper">

    <select id="personStatistic" resultType="com.trs.police.statistic.domain.vo.ArchiveStatisticVO">
         SELECT
            SUBSTRING(c.control_station, 1,6) as areaCode,
            count(*) as allCount,
            sum(case when p.create_time between #{startTime} and #{endTime} then 1 else 0 end) as createCount,
            sum(case when p.update_time between #{startTime} and #{endTime} then 1 else 0 end ) as updateCount
        from t_profile_person p
        left JOIN t_profile_person_police_control c on p.id = c.person_id
        group BY SUBSTRING(c.control_station, 1,6) order by areaCode
    </select>
    <select id="groupStatistic" resultType="com.trs.police.statistic.domain.vo.ArchiveStatisticVO">
         SELECT
            SUBSTRING(c.control_station, 1,6) as areaCode,
            count(*) as allCount,
            sum(case when p.create_time>'2025-06-01 00:00:00' then 1 else 0 end) as createCount,
            sum(case when p.update_time>'2025-06-01 00:00:00' then 1 else 0 end ) as updateCount
         from t_profile_person p
            left JOIN t_profile_person_police_control c on p.id = c.person_id
         group BY SUBSTRING(c.control_station, 1,6) order by areaCode
    </select>
    <select id="eventStatistic" resultType="com.trs.police.statistic.domain.vo.ArchiveStatisticVO">
         SELECT
            SUBSTRING(p.control_station, 1,6) as areaCode,
            count(*) as allCount,,
            sum(case when p.create_time>'2025-03-01 00:00:00' then 1 else 0 end) as createCount,
            sum(case when p.update_time>'2025-03-01 00:00:00' then 1 else 0 end ) as updateCount
         from t_profile_event p
         group BY SUBSTRING(p.control_station, 1,6) order by areaCode
    </select>
    <select id="eventStatisticByPoliceStation"
            resultType="com.trs.police.statistic.domain.vo.ArchiveStatisticVO">
        
    </select>
</mapper>