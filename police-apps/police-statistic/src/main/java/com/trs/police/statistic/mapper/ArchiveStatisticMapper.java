package com.trs.police.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.police.statistic.domain.vo.ArchiveStatisticVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
public interface ArchiveStatisticMapper extends BaseMapper<ArchiveStatisticVO> {

    /**
     * 人员统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<ArchiveStatisticVO> personStatistic(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 群体统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<ArchiveStatisticVO> groupStatistic(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 事件统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<ArchiveStatisticVO> eventStatistic(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 事件统计-派出所
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    List<ArchiveStatisticVO> eventStatisticByPoliceStation(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
