package com.trs.police.statistic.domain.vo;

import com.trs.common.pojo.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS</p>
 * <p>Copyright:    Copyright (c) 2004-2025</p>
 * <p>Company:      www.trs.com.cn</p>
 * 类描述：
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2025/6/6 11:52
 * @since 1.0
 */
@Data
public class ArchiveStatisticDetailVO extends BaseVO {
    /**
     * 地域代码
     */
    private String areaCode;

    /**
     * 地域名称
     */
    private String areaName;

    /**
     * 群体-数量
     */
    private Long groupAllCount = 0L;

    /**
     * 群体-新增数量
     */
    private Long groupCreateCount = 0L;

    /**
     * 群体-更新数量
     */
    private Long groupUpdateCount = 0L;

    /**
     * 人员-数量
     */
    private Long personAllCount = 0L;

    /**
     * 人员-新增数量
     */
    private Long personCreateCount = 0L;

    /**
     * 人员-更新数量
     */
    private Long personUpdateCount = 0L;

    /**
     * 事件-数量
     */
    private Long eventAllCount = 0L;

    /**
     * 事件-新增数量
     */
    private Long eventCreateCount = 0L;

    /**
     * 事件-辖区登记数量
     */
    private Long enevtXqdjCount = 0L;
}
