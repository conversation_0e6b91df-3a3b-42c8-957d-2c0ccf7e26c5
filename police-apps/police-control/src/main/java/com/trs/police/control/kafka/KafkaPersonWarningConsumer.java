package com.trs.police.control.kafka;

import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.common.core.entity.WarningSourceTypeEntity;
import com.trs.police.common.core.utils.JsonUtil;
import com.trs.police.control.kafka.v2.flow.WarningMessageConsume;
import com.trs.police.control.properties.KafkaPersonWarningConsumerProperties;
import com.trs.police.control.service.SourceService;
import com.trs.police.control.service.WarningProcessService;
import com.trs.web.builder.util.BeanFactoryHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * kafka 人员轨迹预警消费者
 *
 * <AUTHOR>
 * @date 2023/06/30
 */
@Component
@Slf4j
@ConditionalOnBean(value = KafkaPersonWarningConsumerProperties.class)
@DependsOn("kafkaKerberosAutoConfigure")
public class KafkaPersonWarningConsumer {
    private final KafkaConsumer<String, String> consumer;

    private final KafkaPersonWarningConsumerProperties properties;

    @Autowired
    private WarningMessageConsume warningMessageConsume;
    private final SourceService sourceService;

    private final WarningProcessService warningProcessService;

    public KafkaPersonWarningConsumer(KafkaPersonWarningConsumerProperties properties, SourceService sourceService, WarningProcessService warningProcessService) {
        this.properties = properties;
        this.sourceService = sourceService;
        this.warningProcessService = warningProcessService;
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, properties.getBootStrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, properties.getGroupId());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, properties.getMaxPollRecords());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, properties.getAutoOffsetReset());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);//关闭自动提交kafka事务，批量处理时控制回滚策略
        if (!CollectionUtils.isEmpty(properties.getProperties())) {
            props.putAll(properties.getProperties());
        }
        if (Boolean.TRUE.equals(properties.getEnableKerberos())) {
            props.put("security.protocol", "SASL_PLAINTEXT");
            props.put("sasl.kerberos.service.name", "kafka");
            props.put("kerberos.domain.name", "hadoop.hadoop.com");
        }
        consumer = new KafkaConsumer<>(props);
        consumer.subscribe(List.of(properties.getTopic()));
    }

    /**
     * 消费人员预警信息
     */
    @Scheduled(fixedDelay = 1L)
    public void consumer() {
        final ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(properties.getPollDuration()));
        log.info("本批次数据共消费：{} 条", records.count());
        List<ConsumerRecord<String, String>> recordList = new ArrayList<>(records.count());
        records.forEach(recordList::add);
        String key = BeanFactoryHolder.getEnv().getProperty("com.trs.kafka.consumer.warningPersonType","default");
        switch (key){
            case "refactoredMethod":
                List<String> messages = recordList.stream().map(e -> e.value()).collect(Collectors.toList());
                warningMessageConsume.consumePersonWarningMessage(messages);
                break;
            default:
                defaultReceivePersonMessage(recordList);
                break;
        }
        //处理该批次消费到的数据
        log.info("本批次消费数据入库完毕！");
        //提交kafka事务
        consumer.commitSync();
    }

    private void defaultReceivePersonMessage(List<ConsumerRecord<String, String>> recordList) {
        recordList.parallelStream().forEach(record -> {
            String message = record.value();
            if (StringUtils.isBlank(message)) {
                log.error("receive warning message blank!");
            }
            WarningDTO warningDTO = JsonUtil.parseObject(message, WarningDTO.class);
            if (warningDTO != null) {
                log.info("解析人员预警消息成功！内容：{}", message);
                try {
                    WarningSourceTypeEntity config = sourceService.getWarningSourceTypeByEnName(warningDTO.getEnName());
                    if (Objects.isNull(config)) {
                        log.info("没有轨迹类型为：{} 的配置，丢弃该条预警！预警内容: {}", warningDTO.getEnName(), warningDTO);
                        warningProcessService.receiveNoConfigWarningMessage(warningDTO, message);
                        warningProcessService.sendWebHookForNoConfigMessage(warningDTO, message);
                    } else {
                        sourceService.saveOrUpdateSource(List.of(warningDTO.getSensingMessage()));
                        warningProcessService.receivePersonWarningMessage(warningDTO);
                    }                } catch (Exception exception) {
                    log.info("异常预警数据：", exception);
                }
            }
        });
    }
}
