package com.trs.police.control.domain.entity.warning;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.db.sdk.annotations.IndexName;
import com.trs.db.sdk.annotations.TableField;
import com.trs.db.sdk.annotations.TableName;
import com.trs.db.sdk.annotations.TableUUID;
import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorBaseTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorLevelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预警表-ES
 * *@author:wen.wen
 * *@create 2024-12-18 20:52
 **/
@Data
@NoArgsConstructor
@TableName(value = "control_warning")
public class ControlEsWarning {

    private static final long serialVersionUID = 1L;

    @TableUUID
    private Long id; // 主键ID

    @IndexName
    private String indexName;

    @TableField(value = "create_user_id")
    private Long createUserId; // 创建用户ID

    @TableField(value = "create_dept_id")
    private Long createDeptId; // 创建部门ID

    @TableField(value = "create_time")
    private Date createTime; // 创建时间

    @TableField(value = "update_user_id")
    private Long updateUserId; // 更新用户ID

    @TableField(value = "update_dept_id")
    private Long updateDeptId; // 更新部门ID

    @TableField(value = "update_time")
    private Date updateTime; // 更新时间

    /**
     * 布控类型 （人员|群体）
     * 参见：{@link MonitorBaseTypeEnum}
     */
    @TableField(value = "warning_type")
    private String warningType; // 预警类型

    /**
     * 参见：{@link MonitorLevelEnum}
     */
    @TableField(value = "warning_level")
    private Integer warningLevel; // 预警级别

    @TableField(value = "content")
    private String content; // 预警详情

    @TableField(value = "warning_time")
    private Date warningTime; // 预警时间

    @TableField(value = "monitor_id")
    private Long monitorId; // 布控ID

    @TableField(value = "model_id", multivalue = true)
    private String modelId; // 模型ID,多值以分号分割

    @TableField(value = "group_id")
    private Long groupId; // 群体ID

    @TableField(value = "group_label", multivalue = true)
    private String groupLabel; // 群体标签，多值以分号分割

    /**
     * {@link ControlTypeEnum}
     */
    @TableField(value = "control_type")
    private Integer controlType; // 管控类型 1=布控 2=常控

    @TableField(value = "activity_address")
    private String activityAddress; // 活动地点

    @TableField(value = "activity_time")
    private Date activityTime; // 活动时间

    @TableField(value = "warning_status")
    private Integer warningStatus = 1; // 流程状态，1：待签收，2：已签收，3：已反馈，4：已完结

    @TableField(value = "is_handle")
    private boolean isHandle;

    @TableField(value = "handle_measure")
    private Integer handleMeasure; //处置措施 1:抓捕 2:管控 3:其他

    @TableField(value = "person_label", multivalue = true)
    private String personLabel; // 人员标签，多值以分号分割

    @TableField(value = "person_name")
    private String personName; //人员姓名

    @TableField(value = "person_id_number")
    private String personIdNumber; //人员证件号码

    @TableField(value = "person_id_type")
    private Integer personIdType; //人员证件类型

    @TableField(value = "area_id", multivalue = true)
    private String areaId; // 命中的区域ID

    @TableField(value = "place_code", multivalue = true)
    private String placeCode; // 命中的场所code

    @TableField(value = "fx_warning_status")
    private Integer fxWarningStatus; // FX预警状态 1 未签收 2 已签收 3 已布控 4 已研判

    @TableField(value = "hit_subject")
    private Long hitSubject; // 命中专题

    @TableField(value = "hit_subject_scene")
    private Long hitSubjectScene; // 命中场景

    /**
     * 通知实体，json结构：
     * [{
     * "userId": 0,       // 用户ID，Integer类型
     * "deptId": 0,       // 部门ID，Integer类型
     * "notifyType": 0,   // 通知类型，Integer类型
     * "isRead": 0        // 是否已读，Integer类型
     * }]
     */
    @TableField(value = "notify_entity", objectValue = true)
    private String notifyEntity; // 通知实体

    @TableField(value = "sign_overdue")
    private Boolean signOverdue; // 是否签收预期

    @TableField(value = "reply_overdue")
    private Boolean replyOverdue; // 反馈逾期

    /**
     * 预警消息中有
     */
    @TableField(value = "source_id")
    private String sourceId; // 感知源code

    /**
     * 来自t_control_warning_source 根据 source_id 查询获取
     */
    @TableField(value = "source_type")
    private Long sourceType; // 感知源类型

    @TableField(value = "datasource_type")
    private String datasourceType; //数据源类型

    /**
     * 来自t_control_warning_source 根据 source_id 查询获取 多值
     */
    @TableField(value = "source_category", multivalue = true)
    private String sourceCategory; // 感知源性质，多值以分号分割

    /**
     * json格式：{
     * "id": 0,            // 区域id，Long类型
     * "name": "",         // 区域名称，String类型
     * "districtCode": "", // 所属行政区划代码，String类型
     * "districtName": ""  // 所属行政区划名称，String类型
     * }
     * 通过sql获取
     *SELECT
     *     *
     * FROM
     *     t_control_important_area area
     * LEFT JOIN t_control_monitor_warning_model tcmwm ON
     *     area.id = tcmwm.important_area_id
     * WHERE
     *     JSON_CONTAINS((SELECT tcm.profile_Target_Id FROM t_control_monitor tcm WHERE id = 1416), CAST(tcmwm.id AS CHAR));
     */
    @TableField(value = "important_area", objectValue = true)
    private String importantAreas; // 关联的重要区域列表

    /**
     * 结构：{
     * "lon" : "104.083207",
     * "lat" : "30.519448"
     * 预警消息中有
     * }
     */
    @TableField(value = "geopoint", objectValue = true)
    private String geopoint; // 地理坐标索引

    /**
     * 发文字号
     */
    @TableField(value = "fwzh")
    private String fwzh;

    /**
     * 首次处置反馈时限
     */
    @TableField(value = "scczfksx")
    private Date scczfksx;

    /**
     * 预警指令ID
     */
    @TableField(value = "ywzjid")
    private String ywzjid;

    /**
     * 布控指令编号
     */
    @TableField(value = "lkzlbh")
    private String lkzlbh;

    /**
     * 签收时限
     */
    @TableField(value = "qssx")
    private Date qssx;

    /**
     * 活动类别
     */
    @TableField(value = "hdlx")
    private String hdlx;

    /**
     * 预警平台
     */
    @TableField(value = "warning_platform")
    private Integer warningPlatform;

    /**
     * 布控平台
     */
    @TableField(value = "monitor_platform", multivalue = true)
    private String monitorPlatform;

    /**
     * 处置责任单位id
     */
    @TableField(value = "monitor_person_unit")
    private Long monitorPersonUnit;

    /**
     * 处置责任单位代码
     */
    @TableField(value = "monitor_unit_code")
    private String monitorUnitCode;

    /**
     * 处置措施要求 云控only
     */
    @TableField(value = "czcsyq")
    private String czcsyq;

    /**
     * 发布责任单位名称 云控only
     */
    @TableField(value = "fbzrdw")
    private String fbzrdw;

    /**
     * 发布责任单位代码 云控only
     */
    @TableField(value = "fbzrdwjgdm")
    private String fbzrdwjgdm;

    /**
     * 活动发起地点区划 云控only
     */
    @TableField(value = "hdfsddqh")
    private String hdfsddqh;

    /**
     * 活动发起地点区划 云控only
     */
    @TableField(value = "hdfsddqhmc")
    private String hdfsddqhmc;

    /**
     * 活动发生地所属社会场所代码 云控only
     */
    @TableField(value = "hdfsddshcsdm")
    private String hdfsddshcsdm;

    /**
     * 活动发生地所属社会场所 云控only
     */
    @TableField(value = "hdfsddshcs")
    private String hdfsddshcs;

    /**
     * 布控级别
     */
    @TableField(value = "monitor_level")
    private Integer monitorLevel;

    /**
     * 常控级别
     */
    @TableField(value = "regular_level")
    private Integer regularLevel;

    /**
     * 设置重要区域
     *
     * @param areaId 地域ID
     */
    public void putAreaId(List<Long> areaId) {
        this.areaId = join(areaId);
    }

    /**
     * 获取重要区域list
     *
     * @return 重要区域list
     */
    public List<Long> areaIdList() {
        return splitToLongList(this.areaId);
    }

    /**
     * 设置模型ID
     *
     * @param modelId 模型ID
     */
    public void putModelId(List<Long> modelId) {
        this.modelId = join(modelId);
    }

    /**
     * 获取模型ID list
     *
     * @return 模型ID list
     */
    public List<Long> modelIdList() {
        return splitToLongList(this.modelId);
    }

    /**
     * 设置群体标签
     *
     * @param groupLabel 群体标签
     */
    public void putGroupLabel(List<Long> groupLabel) {
        this.groupLabel = join(groupLabel);
    }

    /**
     * 获取群体标签list
     *
     * @return 群体标签list
     */
    public List<Long> groupLabelList() {
        return splitToLongList(this.groupLabel);
    }

    /**
     * 设置场所code
     *
     * @param placeCode 场所code
     */
    public void putPlaceCode(List<Long> placeCode) {
        this.placeCode = join(placeCode);
    }

    /**
     * 获取场所code list
     *
     * @return 场所code list
     */
    public List<Long> placeCodeList() {
        return splitToLongList(this.placeCode);
    }

    /**
     * 设置人员标签
     *
     * @param personLabel 人员标签
     */
    public void putPersonLabel(List<Long> personLabel) {
        this.personLabel = join(personLabel);
    }

    /**
     * 获取人员标签list
     *
     * @return 人员标签list
     */
    public List<Long> personLabelList() {
        return splitToLongList(this.personLabel);
    }

    /**
     * 设置感知源源类型
     *
     * @param sourceCategory 感知源类型
     */
    public void putSourceCategory(List<Long> sourceCategory) {
        this.sourceCategory = join(sourceCategory);
    }

    /**
     * 获取感知源源类型list
     *
     * @return 感知源源类型list
     */
    public List<Long> sourceCategoryList() {
        return splitToLongList(this.sourceCategory);
    }

    /**
     * 设置布控平台
     *
     * @param monitorPlatform 布控平台
     */
    public void putMonitorPlatform(List<Long> monitorPlatform) {
        this.monitorPlatform = join(monitorPlatform);
    }

    /**
     * 获取布控平台
     *
     * @return 布控平台
     */
    public List<Long> monitorPlatformList() {
        return splitToLongList(this.monitorPlatform);
    }

    /**
     * 设置地理坐标
     *
     * @param longitude 经度
     * @param latitude  纬度
     */
    public void putGeopoint(Double longitude, Double latitude) {
        if (longitude != null && latitude != null) {
            JSONObject json = new JSONObject();
            json.put("lon", longitude);
            json.put("lat", latitude);
            this.geopoint = json.toJSONString();
        }
    }

    /**
     * 将list转为字符串，以分号分割
     *
     * @param list list
     * @return String
     */
    private String join(List<? extends Number> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return list.stream().map(StringUtils::toStringValue).collect(Collectors.joining(";"));
    }

    private List<Long> splitToLongList(String joinStr) {
        if (org.apache.commons.lang3.StringUtils.isBlank(joinStr)) {
            return Collections.emptyList();
        } else {
            return Arrays.stream(joinStr.split(";")).map(Long::parseLong).collect(Collectors.toList());
        }
    }
}
