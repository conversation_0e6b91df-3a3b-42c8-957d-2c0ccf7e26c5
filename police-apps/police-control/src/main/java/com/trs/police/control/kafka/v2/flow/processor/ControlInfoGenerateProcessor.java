package com.trs.police.control.kafka.v2.flow.processor;

import com.trs.data.exception.ProcessException;
import com.trs.data.processor.IBatchDataProcessor;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.dto.HitInfo;
import com.trs.police.common.core.dto.WarningDTO;
import com.trs.police.control.domain.builder.control.IControlInfoBuilder;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.kafka.v2.context.WarningMessageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * *@author:wen.wen
 * *@create 2025-01-23 18:32
 **/
@Component
@Slf4j
public class ControlInfoGenerateProcessor implements IBatchDataProcessor<WarningMessageContext, WarningMessageContext> {

    @Override
    public List<WarningMessageContext> process(List<WarningMessageContext> warningMessageContexts) throws ProcessException {
        for (WarningMessageContext context : warningMessageContexts) {
            try {
                WarningDTO dto = context.getWarningDTO();
                final HitInfo hitInfo = dto.getHitInfo();
                GroupWarningDTO.Track trackVO = warningDto2Track(dto);
                //判断经纬度是否合法
                if (!isValidCoordinates(trackVO.getSensingMessage().getLongitude(), trackVO.getSensingMessage().getLatitude())) {
                    log.info("预警轨迹经纬度不合法，丢弃该条预警! 轨迹内容：{}", dto);
                    continue;
                }
                //生成controlInfo
                final ControlInfo controlInfo = IControlInfoBuilder.builder(context.getControlType().getEnName())
                        .buildControlInfo(hitInfo.getSubscribeCode(), List.of(trackVO));
                context.setMonitorId(controlInfo.getId());
                context.setTrackVO(trackVO);
                context.setControlInfo(controlInfo);
            } catch (Exception e) {
                log.error("处理预警信息异常", e);
            }
        }

        return warningMessageContexts;
        //return List.of();
    }

    /**
     * 校验经纬度是否合法
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return true/false
     */
    protected boolean isValidCoordinates(Double longitude, Double latitude) {
        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            return true;
        }
        return longitude >= -180.0 && longitude <= 180.0 && latitude >= -90.0 && latitude <= 90.0;
    }

    private GroupWarningDTO.Track warningDto2Track(WarningDTO dto) {
        GroupWarningDTO.Track track = new GroupWarningDTO.Track();
        track.setSensingMessage(dto.getSensingMessage());
        track.setTrackDetail(dto.getTrackDetail());
        track.setEnName(dto.getEnName());
        track.setName(dto.getName());
        track.setIdentifier(dto.getIdentifier());
        track.setIdentifierType(dto.getIdentifierType());
        track.setEventTime(dto.getEventTime());
        return track;
    }
}
