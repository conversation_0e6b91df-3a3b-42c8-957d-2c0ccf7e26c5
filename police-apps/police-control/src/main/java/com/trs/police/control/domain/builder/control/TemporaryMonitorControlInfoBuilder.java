package com.trs.police.control.domain.builder.control;

import com.trs.police.common.core.constant.enums.ControlTypeEnum;
import com.trs.police.common.core.constant.enums.MonitorStatusEnum;
import com.trs.police.common.core.dto.GroupWarningDTO;
import com.trs.police.common.core.utils.BeanUtil;
import com.trs.police.common.core.vo.permission.SimpleUserVO;
import com.trs.police.common.core.vo.permission.UserDeptVO;
import com.trs.police.common.core.vo.profile.PersonVO;
import com.trs.police.common.openfeign.starter.service.ProfileService;
import com.trs.police.control.domain.entity.monitor.MonitorEntity;
import com.trs.police.control.domain.vo.ControlInfo;
import com.trs.police.control.mapper.MonitorMapper;
import com.trs.police.control.mapper.MonitorWarningModelRelationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 临控 构建 ControlInfo
 * *@author:wen.wen
 * *@create 2024-12-26 16:20
 **/
@Slf4j
@Component
public class TemporaryMonitorControlInfoBuilder extends AbstractControlInfoBuilder {

    @Override
    public void generateControlInfo(ControlInfo c, List<GroupWarningDTO.Track> tracks) {
        try {
            MonitorMapper monitorMapper = BeanUtil.getBean(MonitorMapper.class);
            final MonitorEntity monitor = monitorMapper.selectById(c.getId());
            c.setIsInMonitor(monitor != null && monitor.getMonitorStatus() == MonitorStatusEnum.MONITORING);
            if (monitor != null) {
                c.setName(monitor.getMonitorTitle());
                c.setTargetType(monitor.getMonitorType());
                c.setCreateUser(new UserDeptVO(monitor.getCreateUserId(), monitor.getCreateDeptId()));
                ControlInfo.WarningInfo w = new ControlInfo.WarningInfo();
                w.setLevel(monitor.getMonitorLevel());
                w.setNotifyPerson(Arrays.stream(monitor.getNotifyPerson()).map(SimpleUserVO::toUserDeptVO).collect(
                        Collectors.toList()));
                MonitorWarningModelRelationMapper monitorWarningModelRelationMapper =
                        BeanUtil.getBean(MonitorWarningModelRelationMapper.class);
                w.setModelIds(monitorWarningModelRelationMapper.selectModelIdsByMonitorId(c.getId()));
                ProfileService profileService = BeanUtil.getBean(ProfileService.class);
                tracks.forEach(track -> {
                    List<PersonVO> persons = profileService.findByIdentifier(track.getIdentifier(), track.getIdentifierType());
                    persons.stream().filter(item -> monitor.getProfileTargetId().contains(item.getId())).findFirst().ifPresent(track::setPerson);
                });
                if (Boolean.TRUE.equals(monitor.getNotifyWarningArea())) {
                    w.setNotifyWarningAreaPerson(
                            getNotifyWarningAreaPerson(tracks).stream()
                                    .filter(item -> !w.getNotifyPerson().contains(item))
                                    .collect(Collectors.toList()));
                }
                if (Boolean.TRUE.equals(monitor.getNotifyControlDept())) {
                    w.setNotifyControlDeptPerson(
                            getNotifyControlDeptPerson(tracks).stream()
                                    .filter(item -> !w.getNotifyPerson().contains(item))
                                    .collect(Collectors.toList()));
                }
                if (Boolean.TRUE.equals(monitor.getNotifyWarningArea())) {
                    w.setNotifyWarningAreaPerson(
                            getNotifyWarningAreaPerson(tracks).stream()
                                    .filter(item -> !w.getNotifyPerson().contains(item)
                                            && !w.getNotifyControlDeptPerson().contains(item))
                                    .collect(Collectors.toList()));
                }
                if (Boolean.TRUE.equals(monitor.getWarningMessageStatus())) {
                    List<UserDeptVO> phoneMessageUsers = Stream.of(w.getNotifyPerson(), w.getNotifyWarningAreaPerson(),
                                    w.getNotifyControlDeptPerson())
                            .filter(Objects::nonNull)
                            .flatMap(List::stream)
                            .distinct()
                            .collect(Collectors.toList());
                    w.setPhoneMessageUser(phoneMessageUsers);
                }
                c.setWarningInfo(List.of(w));
            }
        } catch (Exception e) {
            log.error("临控构建ControlInfo异常", e);
        }
    }

    @Override
    public ControlTypeEnum controlType() {
        return ControlTypeEnum.MONITOR;
    }
}
