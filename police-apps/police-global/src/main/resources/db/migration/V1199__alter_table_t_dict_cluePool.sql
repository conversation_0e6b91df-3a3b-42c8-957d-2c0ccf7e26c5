DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    -- 声明变量用于存储各级ID
    DECLARE topid INT;
    DECLARE yizhiwu INT;
    DECLARE yizhiwuall INT;
    DECLARE qzzx INT; -- 新增情指中心
    DECLARE zcba INT;
    DECLARE jcgk INT;
    DECLARE zhxlfk INT;
    DECLARE jzfl INT;
    DECLARE jzflall INT;
    DECLARE xz INT;
    DECLARE za INT;
    DECLARE zb INT;
    DECLARE jj INT;
    DECLARE xd INT;
    DECLARE ywfl INT;
    DECLARE ywflall INT;
    DECLARE zal INT;
    DECLARE xsl INT;
    DECLARE jtl INT;
    DECLARE skl INT;
    DECLARE swl INT;
    DECLARE pcs INT;
    DECLARE pcsall INT;

    -- 先删除旧数据
    DELETE FROM `t_dict` WHERE `type` LIKE 'portal_clue_pool_navigation%';

    -- 插入顶级组
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES ('portal_clue_pool_navigation_group', 0, '门户线索池导航组', 0, 'top', 0, NULL, NULL, NULL, 1);
    SET topid = LAST_INSERT_ID();
    UPDATE `t_dict` SET `p_id` = topid WHERE `id` = topid;

    -- 一指五中心
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (topid, 'portal_clue_pool_navigation', 1, '一指五中心', 0, 'group_one', 1, NULL, NULL, NULL, 1);
    SET yizhiwu = LAST_INSERT_ID();

    -- 一指五中心 - 全部
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (yizhiwu, 'portal_clue_pool_navigation', 2, '全部', 1, 'group_two', 1, NULL, NULL, NULL, 1);
    SET yizhiwuall = LAST_INSERT_ID();

    -- 一指五中心 - 全部下的模块 (按顺序设置code)
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (yizhiwuall, 'portal_clue_pool_navigation', 3, '吸毒人员驾驶机动车', 2, 'module', 101, NULL, '1', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 4, '盗窃案件疑似人员', 2, 'module', 102, NULL, '0', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 5, '蝙蝠聊天疑似涉黄人员', 2, 'module', 103, NULL, '5', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 6, '涉黄人员重点部位预警', 2, 'module', 104, NULL, '6', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 7, '隐形涉邪人员线索挖掘', 2, 'module', 105, NULL, '3', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 8, '无证驾驶', 2, 'module', 106, NULL, '2', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 9, '盗窃三车风险预警', 2, 'module', 107, NULL, '100', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 10, '扒窃风险预警', 2, 'module', 108, NULL, '101', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 11, '隐形涉疆人员线索挖掘', 2, 'module', 109, NULL, '4', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 12, '疑似流动人口', 2, 'module', 110, NULL, '104', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 13, '三失三气驾驶机动车', 2, 'module', 111, NULL, '110', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 14, '涉毒人员聚集风险', 2, 'module', 112, NULL, '112', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 15, '五失一偏风险人员', 2, 'module', 113, NULL, '114', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 16, '重复报警人员', 2, 'module', 115, NULL, '115', NULL, 1),
        (yizhiwuall, 'portal_clue_pool_navigation', 17, '民转刑风险人员', 2, 'module', 116, NULL, '116', NULL, 1);


    -- 一指五中心 - 情指中心 (新增)
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (yizhiwu, 'portal_clue_pool_navigation', 18, '情指中心', 1, 'group_two', 2, NULL, NULL, NULL, 1);
    SET qzzx = LAST_INSERT_ID();

    -- 一指五中心 - 情指中心下的模块 (新增)
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (qzzx, 'portal_clue_pool_navigation', 19, '重复报警人员', 18, 'module', 115, NULL, '115', NULL, 1),
        (qzzx, 'portal_clue_pool_navigation', 20, '民转刑风险人员', 18, 'module', 116, NULL, '116', NULL, 1);

    -- 一指五中心 - 侦查办案管理中心
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (yizhiwu, 'portal_clue_pool_navigation', 21, '侦查办案管理中心', 1, 'group_two', 3, NULL, NULL, NULL, 1);
    SET zcba = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (zcba, 'portal_clue_pool_navigation', 22, '蝙蝠聊天疑似涉黄人员', 21, 'module', 150, NULL, '5', NULL, 1),
        (zcba, 'portal_clue_pool_navigation', 23, '吸毒人员驾驶机动车', 21, 'module', 151, NULL, '1', NULL, 1),
        (zcba, 'portal_clue_pool_navigation', 24, '盗窃案件疑似人员', 21, 'module', 152, NULL, '0', NULL, 1),
        (zcba, 'portal_clue_pool_navigation', 25, '盗窃三车风险预警', 21, 'module', 153, NULL, '100', NULL, 1),
        (zcba, 'portal_clue_pool_navigation', 26, '扒窃风险预警', 21, 'module', 154, NULL, '101', NULL, 1);

    -- 一指五中心 - 基础管控中心
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (yizhiwu, 'portal_clue_pool_navigation', 27, '基础管控中心', 1, 'group_two', 4, NULL, NULL, NULL, 1);
    SET jcgk = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (jcgk, 'portal_clue_pool_navigation', 28, '涉黄人员重点部位预警', 27, 'module', 200, NULL, '6', NULL, 1),
        (jcgk, 'portal_clue_pool_navigation', 29, '疑似流动人口', 27, 'module', 201, NULL, '104', NULL, 1),
        (jcgk, 'portal_clue_pool_navigation', 30, '三失三气驾驶机动车', 27, 'module', 202, NULL, '110', NULL, 1),
        (jcgk, 'portal_clue_pool_navigation', 31, '涉毒人员聚集风险', 27, 'module', 203, NULL, '112', NULL, 1),
        (jcgk, 'portal_clue_pool_navigation', 32, '五失一偏风险人员', 27, 'module', 204, NULL, '114', NULL, 1);

    -- 一指五中心 - 指挥巡逻防控中心
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (yizhiwu, 'portal_clue_pool_navigation', 33, '指挥巡逻防控中心', 1, 'group_two', 5, NULL, NULL, NULL, 1);
    SET zhxlfk = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (zhxlfk, 'portal_clue_pool_navigation', 34, '盗窃三车风险预警', 33, 'module', 300, NULL, '100', NULL, 1),
        (zhxlfk, 'portal_clue_pool_navigation', 35, '扒窃风险预警', 33, 'module', 301, NULL, '101', NULL, 1);

    -- 一指五中心 - 其他中心
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (yizhiwu, 'portal_clue_pool_navigation', 36, '指挥室', 1, 'group_two', 6, NULL, NULL, NULL, 1),
        (yizhiwu, 'portal_clue_pool_navigation', 37, '执法办案管理中心', 1, 'group_two', 7, NULL, NULL, NULL, 1),
        (yizhiwu, 'portal_clue_pool_navigation', 38, '网络攻防中心', 1, 'group_two', 8, NULL, NULL, NULL, 1);

    -- 警种分类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (topid, 'portal_clue_pool_navigation', 39, '警种分类', 0, 'group_one', 2, NULL, NULL, NULL, 1);
    SET jzfl = LAST_INSERT_ID();

    -- 警种分类 - 全部
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 40, '全部', 39, 'group_two', 1, NULL, NULL, NULL, 1);
    SET jzflall = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (jzflall, 'portal_clue_pool_navigation', 41, '吸毒人员驾驶机动车', 40, 'module', 101, NULL, '1', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 42, '盗窃案件疑似人员', 40, 'module', 102, NULL, '0', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 43, '蝙蝠聊天疑似涉黄人员', 40, 'module', 103, NULL, '5', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 44, '涉黄人员重点部位预警', 40, 'module', 104, NULL, '6', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 45, '隐形涉邪人员线索挖掘', 40, 'module', 105, NULL, '3', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 46, '无证驾驶', 40, 'module', 106, NULL, '2', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 47, '盗窃三车风险预警', 40, 'module', 107, NULL, '100', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 48, '扒窃风险预警', 40, 'module', 108, NULL, '101', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 49, '隐形涉疆人员线索挖掘', 40, 'module', 109, NULL, '4', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 50, '疑似流动人口', 40, 'module', 110, NULL, '104', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 51, '三失三气驾驶机动车', 40, 'module', 111, NULL, '110', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 52, '涉毒人员聚集风险', 40, 'module', 112, NULL, '112', NULL, 1),
        (jzflall, 'portal_clue_pool_navigation', 53, '五失一偏风险人员', 40, 'module', 113, NULL, '114', NULL, 1);

    -- 警种分类 - 刑侦
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 54, '刑侦', 39, 'group_two', 2, NULL, NULL, NULL, 1);
    SET xz = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (xz, 'portal_clue_pool_navigation', 55, '盗窃案件疑似人员', 54, 'module', 150, NULL, '0', NULL, 1),
        (xz, 'portal_clue_pool_navigation', 56, '蝙蝠聊天疑似涉黄人员', 54, 'module', 151, NULL, '5', NULL, 1),
        (xz, 'portal_clue_pool_navigation', 57, '盗窃三车风险预警', 54, 'module', 153, NULL, '100', NULL, 1),
        (xz, 'portal_clue_pool_navigation', 58, '扒窃风险预警', 54, 'module', 154, NULL, '101', NULL, 1);

    -- 警种分类 - 治安
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 59, '治安', 39, 'group_two', 3, NULL, NULL, NULL, 1);
    SET za = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (za, 'portal_clue_pool_navigation', 60, '涉黄人员重点部位预警', 59, 'module', 104, NULL, '6', NULL, 1),
        (za, 'portal_clue_pool_navigation', 61, '疑似流动人口', 59, 'module', 110, NULL, '104', NULL, 1),
        (za, 'portal_clue_pool_navigation', 62, '三失三气驾驶机动车', 59, 'module', 111, NULL, '110', NULL, 1),
        (za, 'portal_clue_pool_navigation', 63, '涉毒人员聚集风险', 59, 'module', 112, NULL, '112', NULL, 1),
        (za, 'portal_clue_pool_navigation', 64, '五失一偏风险人员', 59, 'module', 113, NULL, '114', NULL, 1);

    -- 警种分类 - 政保
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 65, '政保', 39, 'group_two', 4, NULL, NULL, NULL, 1);
    SET zb = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (zb, 'portal_clue_pool_navigation', 66, '隐形涉邪人员线索挖掘', 65, 'module', 105, NULL, '3', NULL, 1),
        (zb, 'portal_clue_pool_navigation', 67, '隐形涉疆人员线索挖掘', 65, 'module', 109, NULL, '4', NULL, 1);

    -- 警种分类 - 交警
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 68, '交警', 39, 'group_two', 5, NULL, NULL, NULL, 1);
    SET jj = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (jj, 'portal_clue_pool_navigation', 69, '吸毒人员驾驶机动车', 68, 'module', 101, NULL, '1', NULL, 1),
        (jj, 'portal_clue_pool_navigation', 70, '无证驾驶', 68, 'module', 106, NULL, '2', NULL, 1);

    -- 警种分类 - 巡大
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (jzfl, 'portal_clue_pool_navigation', 71, '巡大', 39, 'group_two', 6, NULL, NULL, NULL, 1);
    SET xd = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (xd, 'portal_clue_pool_navigation', 72, '盗窃三车风险预警', 71, 'module', 153, NULL, '100', NULL, 1),
        (xd, 'portal_clue_pool_navigation', 73, '扒窃风险预警', 71, 'module', 154, NULL, '101', NULL, 1);

    -- 所属派出所
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (topid, 'portal_clue_pool_navigation', 74, '所属派出所', 0, 'group_one', 3, NULL, NULL, NULL, 1);
    SET pcs = LAST_INSERT_ID();

    -- 所属派出所 - 全部
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (pcs, 'portal_clue_pool_navigation', 75, '全部', 74, NULL, 1, NULL, NULL, NULL, 1);
    SET pcsall = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (pcsall, 'portal_clue_pool_navigation', 76, '吸毒人员驾驶机动车', 75, 'module', 101, NULL, '1', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 77, '盗窃案件疑似人员', 75, 'module', 102, NULL, '0', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 78, '蝙蝠聊天疑似涉黄人员', 75, 'module', 103, NULL, '5', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 79, '涉黄人员重点部位预警', 75, 'module', 104, NULL, '6', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 80, '隐形涉邪人员线索挖掘', 75, 'module', 105, NULL, '3', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 81, '无证驾驶', 75, 'module', 106, NULL, '2', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 82, '盗窃三车风险预警', 75, 'module', 107, NULL, '100', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 83, '扒窃风险预警', 75, 'module', 108, NULL, '101', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 84, '隐形涉疆人员线索挖掘', 75, 'module', 109, NULL, '4', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 85, '疑似流动人口', 75, 'module', 110, NULL, '104', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 86, '三失三气驾驶机动车', 75, 'module', 111, NULL, '110', NULL, 1),
        (pcsall, 'portal_clue_pool_navigation', 87, '涉毒人员聚集风险', 75, 'module', 112, NULL, '112', NULL, 1);

    -- 业务分类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (topid, 'portal_clue_pool_navigation', 88, '业务分类', 0, 'group_one', 4, NULL, NULL, NULL, 1);
    SET ywfl = LAST_INSERT_ID();

    -- 业务分类 - 全部
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 89, '全部', 88, 'group_two', 1, NULL, NULL, NULL, 1);
    SET ywflall = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (ywflall, 'portal_clue_pool_navigation', 90, '吸毒人员驾驶机动车', 89, 'module', 101, NULL, '1', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 91, '盗窃案件疑似人员', 89, 'module', 102, NULL, '0', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 92, '蝙蝠聊天疑似涉黄人员', 89, 'module', 103, NULL, '5', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 93, '涉黄人员重点部位预警', 89, 'module', 104, NULL, '6', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 94, '隐形涉邪人员线索挖掘', 89, 'module', 105, NULL, '3', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 95, '无证驾驶', 89, 'module', 106, NULL, '2', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 96, '盗窃三车风险预警', 89, 'module', 107, NULL, '100', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 97, '扒窃风险预警', 89, 'module', 108, NULL, '101', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 98, '隐形涉疆人员线索挖掘', 89, 'module', 109, NULL, '4', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 99, '疑似流动人口', 89, 'module', 110, NULL, '104', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 100, '三失三气驾驶机动车', 89, 'module', 111, NULL, '110', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 101, '涉毒人员聚集风险', 89, 'module', 112, NULL, '112', NULL, 1),
        (ywflall, 'portal_clue_pool_navigation', 102, '五失一偏风险人员', 89, 'module', 113, NULL, '114', NULL, 1);

    -- 业务分类 - 治安类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 103, '治安类', 88, 'group_two', 2, NULL, NULL, NULL, 1);
    SET zal = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (zal, 'portal_clue_pool_navigation', 104, '涉黄人员重点部位预警', 103, 'module', 104, NULL, '6', NULL, 1),
        (zal, 'portal_clue_pool_navigation', 105, '疑似流动人口', 103, 'module', 110, NULL, '104', NULL, 1),
        (zal, 'portal_clue_pool_navigation', 106, '三失三气驾驶机动车', 103, 'module', 111, NULL, '110', NULL, 1),
        (zal, 'portal_clue_pool_navigation', 107, '涉毒人员聚集风险', 103, 'module', 112, NULL, '112', NULL, 1),
        (zal, 'portal_clue_pool_navigation', 108, '五失一偏风险人员', 103, 'module', 113, NULL, '114', NULL, 1);

    -- 业务分类 - 刑事类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 109, '刑事类', 88, 'group_two', 3, NULL, NULL, NULL, 1);
    SET xsl = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (xsl, 'portal_clue_pool_navigation', 110, '盗窃案件疑似人员', 109, 'module', 102, NULL, '0', NULL, 1),
        (xsl, 'portal_clue_pool_navigation', 111, '蝙蝠聊天疑似涉黄人员', 109, 'module', 103, NULL, '5', NULL, 1),
        (xsl, 'portal_clue_pool_navigation', 112, '盗窃三车风险预警', 109, 'module', 107, NULL, '100', NULL, 1),
        (xsl, 'portal_clue_pool_navigation', 113, '扒窃风险预警', 109, 'module', 108, NULL, '101', NULL, 1);

    -- 业务分类 - 交通类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 114, '交通类', 88, 'group_two', 4, NULL, NULL, NULL, 1);
    SET jtl = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (jtl, 'portal_clue_pool_navigation', 115, '吸毒人员驾驶机动车', 114, 'module', 110, NULL, '1', NULL, 1),
        (jtl, 'portal_clue_pool_navigation', 116, '无证驾驶', 114, 'module', 111, NULL, '2', NULL, 1);

    -- 业务分类 - 涉恐类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 117, '涉恐类', 88, 'group_two', 5, NULL, NULL, NULL, 1);
    SET skl = LAST_INSERT_ID();

    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`) VALUES
        (skl, 'portal_clue_pool_navigation', 118, '隐形涉邪人员线索挖掘', 117, 'module', 105, NULL, '3', NULL, 1),
        (skl, 'portal_clue_pool_navigation', 119, '隐形涉疆人员线索挖掘', 117, 'module', 109, NULL, '4', NULL, 1);

    -- 业务分类 - 涉稳类
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES (ywfl, 'portal_clue_pool_navigation', 120, '涉稳类', 88, 'group_two', 6, NULL, NULL, NULL, 1);
    SET swl = LAST_INSERT_ID();
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;