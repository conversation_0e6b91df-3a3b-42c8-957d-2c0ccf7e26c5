CREATE TABLE if not exists `t_profile_police` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `id_number` varchar(255) DEFAULT NULL COMMENT '证件号码',
  `id_type` int DEFAULT NULL COMMENT '1：身份证，2：护照',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `gender` int DEFAULT NULL COMMENT '性别',
  `promotion_status` int Default NULL COMMENT '晋升状态，码表，type = police_emergency_status',
  `former_name` varchar(255) DEFAULT NULL COMMENT '曾用名',
  `nation` int DEFAULT NULL COMMENT '民族，type = nation',
  `political_status` int DEFAULT NULL COMMENT '政治面貌，政治面貌码表：profile_political_status',
  `martial_status` int DEFAULT NULL COMMENT '婚姻状况，码表profile_martial_status',
  `registered_residence` varchar(255) DEFAULT NULL COMMENT '户籍地区域代码',
  `registered_residence_detail` varchar(255) DEFAULT NULL COMMENT '户籍地详细地址',
  `current_residence` varchar(255) DEFAULT NULL COMMENT '现住址区域代码',
  `current_residence_detail` varchar(255) DEFAULT NULL COMMENT '现住址详细地址',
  `photo` json DEFAULT NULL COMMENT '照片',
  `dept_ids` json DEFAULT NULL COMMENT '所属部门',
  `tel` json DEFAULT NULL COMMENT '联系方式',
  `family_relation_ids` json DEFAULT NULL COMMENT '家庭关系id',
  `deleted` int DEFAULT '0' COMMENT '是否删除',
  `police_number` varchar(255) DEFAULT NULL COMMENT '警号',
  `native_place` varchar(255) DEFAULT NULL COMMENT '籍贯',
  `join_work_date` date DEFAULT NULL COMMENT '参加工作日期',
  `join_public_security_work_date` date DEFAULT NULL COMMENT '参加公安工作日期',
  `score` DOUBLE DEFAULT 0.0 COMMENT '积分',
  `score_detail` json DEFAULT NULL COMMENT '积分详情',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='警员档案表';

CREATE TABLE if not exists `t_police_rank_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `start_time` date NOT NULL COMMENT '任职开始时间',
  `end_time` date DEFAULT NULL COMMENT '任职结束时间',
  `rank_series` int NOT NULL COMMENT '任职序列，码表，type = police_rz',
  `rank_code` int NOT NULL COMMENT '职级，码表，type = police_zj',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='警员职级表';

CREATE TABLE if not exists `t_police_resume_relation` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` BIGINT DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` BIGINT DEFAULT NULL COMMENT '创建用户主键',
  `create_time` TIMESTAMP NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` BIGINT DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` BIGINT DEFAULT NULL COMMENT '更新单位主键',
  `update_time` TIMESTAMP NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `start_time` DATE NOT NULL COMMENT '任职开始时间',
  `end_time` DATE DEFAULT NULL COMMENT '任职结束时间，若勾选“至今”，则可为空',
  `position` int NOT NULL COMMENT '任职职务，码表，type = police_ll_rzzw',
  `position_level` int NOT NULL COMMENT '任职职务级别，码表，type = police_ll_rzzwjb',
  `department` VARCHAR (255) CHARACTER
  SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任职部门（如xxx支队xxx大队）',
  `deleted` TINYINT DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '警员履历表';

CREATE TABLE if not exists `t_police_assist_tibet_xinjiang_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `service_start_time` date NOT NULL COMMENT '服务开始时间',
  `service_end_time` date DEFAULT NULL COMMENT '服务结束时间',
  `service_area` int NOT NULL COMMENT '服务地区，码表，type = police_czyj_fudq',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除，0：否，1：是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='援藏援疆表';

CREATE TABLE if not exists `t_police_education_experience_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `start_time` date NOT NULL COMMENT '在校开始时间（对应界面“在校时间”的起始日期）',
  `end_time` date DEFAULT NULL COMMENT '在校结束时间（对应界面“在校时间”的结束日期）',
  `graduation_school` varchar(255) NOT NULL COMMENT '毕业学校（对应界面“毕业学校”输入值）',         
  `degree` int default null comment '学历，码表，type = t_degree',
  `major` varchar(255) DEFAULT NULL COMMENT '专业（对应界面“专业”输入值）',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除，0：否，1：是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育经历表';

CREATE TABLE if not exists `t_police_family_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `relation` varchar(255)  NOT NULL COMMENT '关系',
  `name` varchar(255)  NOT NULL COMMENT '姓名',
  `id_number` varchar(255) NOT NULL COMMENT '身份证号码',
  `phone_number` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除，0：否；1：是',
  `work_unit` varchar(100) DEFAULT NULL COMMENT '工作单位',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '家庭关系表';

CREATE TABLE if not exists `t_police_professional_technology_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `acquisition_time` date NOT NULL COMMENT '获取时间（对应界面“获取时间”）',
  `technology` int NOT NULL COMMENT '专业技术名称, 码表, type= police_zyjs',
  `description` text DEFAULT NULL COMMENT '描述（对应界面“描述”输入的具体情况）',
  `material_path` json DEFAULT NULL COMMENT '上传材料',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业技术表';

CREATE TABLE if not exists `t_police_professional_lgsj_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `acquisition_time` date NOT NULL COMMENT '立功受奖时间（对应界面“获取时间”）',
  `lgsj` int NOT NULL COMMENT '立功受奖, 码表, type= police_lgsj',
  `description` text DEFAULT NULL COMMENT '描述（对应界面“描述”输入的具体情况）',
  `material_path` json DEFAULT NULL COMMENT '上传材料',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='立功受奖表';

CREATE TABLE if not exists `t_police_professional_wgwj_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `record_time` date NOT NULL COMMENT '记录日期',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `wt_type` int NOT NULL COMMENT '问题类型, 码表，type = police_wgwj',
  `description` text DEFAULT NULL COMMENT '详情',
  `material_path` json DEFAULT NULL COMMENT '上传材料',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='违规违纪表';

CREATE TABLE if not exists `t_police_professional_ndkh_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `assessment_time` date NOT NULL COMMENT '考核日期',
  `assessment_result` int NOT NULL COMMENT '考核结果, 码表，type = police_assessment_result',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='年度考核表';


CREATE TABLE if not exists `t_police_democratic_evaluation_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_dept_id` bigint DEFAULT NULL COMMENT '创建单位主键',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建用户主键',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新用户主键',
  `update_dept_id` bigint DEFAULT NULL COMMENT '更新单位主键',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `evaluation_year` date NOT NULL COMMENT '测评年份（对应界面“测评年份”）',
  `profile_id` bigint NOT NULL COMMENT '关联警员档案表（t_police_profile）的主键',
  `a_ticket_rank` varchar(255) DEFAULT NULL COMMENT 'A票排名（对应界面“A票 - 排名”）',
  `a_ticket_excellent_count` int DEFAULT NULL COMMENT 'A票优秀票数（对应界面“A票 - 优秀的票（前一个框）”）',
  `a_ticket_total_count` int DEFAULT NULL COMMENT 'A票总票数（对应界面“A票 - 优秀的票（后一个框）”）',
  `b_ticket_rank` varchar(255) DEFAULT NULL COMMENT 'B票排名（对应界面“B票 - 排名”）',
  `b_ticket_excellent_count` int DEFAULT NULL COMMENT 'B票优秀票数（对应界面“B票 - 优秀的票（前一个框）”）',
  `b_ticket_total_count` int DEFAULT NULL COMMENT 'B票总票数（对应界面“B票 - 优秀的票（后一个框）”）',
  `deleted` tinyint DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='民主测评表';