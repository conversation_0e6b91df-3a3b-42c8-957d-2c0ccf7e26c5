DELIMITER $$
DROP PROCEDURE IF EXISTS `insert_dict_data` $$
CREATE PROCEDURE insert_dict_data()
BEGIN
    -- 声明变量用于存储各级ID
    DECLARE
    rzxl_id INT; -- 任职序列ID
    DECLARE
    rzxl_one_id INT; -- 任职序列ID
    DECLARE
    zj_id INT; -- 职级ID
    DECLARE
    jwjs_zj_id INT; -- 警务技术职级ID
    DECLARE
    rzzw_id INT; -- 任职职务ID
    DECLARE
    rzzwjb_id INT; -- 任职职务级别ID
    DECLARE
    fwdq_id INT; -- 服务地区ID
    DECLARE
    zyjs_id INT; -- 专业技术名称ID
    DECLARE
    lgsj_id INT; -- 立功受奖ID
    DECLARE
    wgwj_id INT; -- 问题类型ID
    DECLARE
    khjg_id INT; -- 考核结果ID
    DECLARE
    jszt_id INT; -- 晋升状态id
    DECLARE
    age_range_id INT; -- 晋升状态id
    DECLARE
    degree_id INT; -- 学历id
    -- 先删除旧数据
    DELETE
    FROM
      `t_dict`
    WHERE
      `type` LIKE 'police_rz_xl%'
      OR `type` LIKE 'police_zj%'
      OR `type` LIKE 'police_ll_rzzw%'
      OR `type` LIKE 'police_ll_rzzwjb%'
      OR `type` LIKE 'police_czyj_fudq%'
      OR `type` LIKE 'police_zyjs%'
      OR `type` LIKE 'police_lgsj%'
      OR `type` LIKE 'police_wgwj%'
      OR `type` LIKE 'police_assessment_result%'
      OR `type` LIKE 'police_emergency_status%'
      OR `type` LIKE 'police_age_range%'
      OR `type` LIKE 'police_jwjs_zj%'
      OR `type` LIKE 't_degree%';
    -- ========== 任职序列 (police_rz_xl) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_rz_xl_group', 0, '任职序列', 0, '任职序列', 1, NULL, 1, NULL, 1);
    SET rzxl_one_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = rzxl_one_id
    WHERE
      `id` = rzxl_one_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_one_id, 'police_rz_xl', 1, '执法勤务警员职务', 0, '执法勤务警员职务', 1, NULL, 3, NULL, 1),
    (rzxl_one_id, 'police_rz_xl', 12, '警务技术职务', 0, '警务技术职务', 2, NULL, 3, NULL, 1);

    -- ========== 职级 (police_zj) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_zj_group', 0, '职级', 0, '职级', 1, NULL, 1, NULL, 1);
    SET zj_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = zj_id
    WHERE
      `id` = zj_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (zj_id, 'police_zj_one', 1, '执法勤务警员职务', 0, '执法勤务警员职务', 1, NULL, 2, NULL, 1);
     SET rzxl_id = LAST_INSERT_ID();
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_id, 'police_zj', 2, '一级高级警长', 1, '一级高级警长', 1, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 3, '二级高级警长', 1, '二级高级警长', 2, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 4, '三级高级警长', 1, '三级高级警长', 3, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 5, '四级高级警长', 1, '四级高级警长', 4, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 6, '一级警长', 1, '一级警长', 5, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 7, '二级警长', 1, '二级警长', 6, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 8, '三级警长', 1, '三级警长', 7, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 9, '四级警长', 1, '四级警长', 8, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 10, '一级警员', 1, '一级警员', 9, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 11, '二级警员', 1, '二级警员', 10, NULL, 3, NULL, 1);
    -- ========== 职级 (police_zj) ==========
    INSERT INTO `t_dict` (`p_id`,`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (zj_id, 'police_zj', 12, '警务技术职务', 0, '警务技术职务', 2, NULL, 2, NULL, 1);
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzxl_id, 'police_zj', 13, '一级主任', 12, '一级主任', 1, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 14, '二级主任', 12, '二级主任', 2, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 15, '三级主任', 12, '三级主任', 3, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 16, '四级主任  ', 12, '四级主任', 4, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 17, '一级主管', 12, '一级主管', 5, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 18, '二级主管', 12, '二级主管', 6, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 19, '三级主管', 12, '三级主管', 7, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 20, '四级主管', 12, '四级主管', 8, NULL, 3, NULL, 1),
    (rzxl_id, 'police_zj', 21, '警务技术员', 12, '警务技术员', 9, NULL, 3, NULL, 1);
    -- ========== 任职职务 (police_ll_rzzw) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_ll_rzzw_group', 0, '任职职务', 0, '任职职务', 1, NULL, NULL, NULL, 1);
    SET rzzw_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = rzzw_id
    WHERE
      `id` = rzzw_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzzw_id, 'police_ll_rzzw', 1, '支队长', 1, '支队长', 1, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 2, '政委', 1, '政委', 2, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 3, '副支队长', 1, '副支队长', 3, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 4, '主任', 1, '主任', 4, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 5, '副主任', 1, '副主任', 5, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 6, '大队长', 1, '大队长', 6, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 7, '教导员', 1, '教导员', 7, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 8, '副大队长', 1, '副大队长', 8, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 9, '所长', 1, '所长', 9, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 10, '副所长', 1, '副所长', 10, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 11, '科长', 1, '科长', 11, NULL, NULL, NULL, 1),
    (rzzw_id, 'police_ll_rzzw', 12, '副科长', 1, '副科长', 12, NULL, NULL, NULL, 1);
    -- ========== 任职职务级别 (police_ll_rzzwjb) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_ll_rzzwjb_group', 0, '任职职务级别', 0, '任职职务级别', 1, NULL, NULL, NULL, 1);
    SET rzzwjb_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = rzzwjb_id
    WHERE
      `id` = rzzwjb_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (rzzwjb_id, 'police_ll_rzzwjb', 1, '县处级正职', 0, '县处级正职', 1, NULL, NULL, NULL, 1),
    (rzzwjb_id, 'police_ll_rzzwjb', 2, '县处级副职', 0, '县处级副职', 2, NULL, NULL, NULL, 1),
    (rzzwjb_id, 'police_ll_rzzwjb', 3, '部门正职', 0, '部门正职', 3, NULL, NULL, NULL, 1),
    (rzzwjb_id, 'police_ll_rzzwjb', 4, '正科级', 0, '正科级', 4, NULL, NULL, NULL, 1),
    (rzzwjb_id, 'police_ll_rzzwjb', 5, '副科级', 0, '副科级', 5, NULL, NULL, NULL, 1),
    (rzzwjb_id, 'police_ll_rzzwjb', 6, '民警', 0, '民警', 6, NULL, NULL, NULL, 1);
    -- ========== 服务地区 (police_czyj_fudq) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_czyj_fudq_group', 0, '服务地区', 0, '服务地区', 1, NULL, NULL, NULL, 1);
    SET fwdq_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = fwdq_id
    WHERE
      `id` = fwdq_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (fwdq_id, 'police_czyj_fudq', 1, '新疆', 1, '新疆', 1, NULL, NULL, NULL, 1),
    (fwdq_id, 'police_czyj_fudq', 2, '西藏', 1, '西藏', 2, NULL, NULL, NULL, 1),
    (fwdq_id, 'police_czyj_fudq', 3, '凉山', 1, '凉山', 3, NULL, NULL, NULL, 1);
    -- ========== 专业技术名称 (police_zyjs) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_zyjs_group', 0, '法医', 0, '专业技术名称', 1, NULL, NULL, NULL, 1);
    SET zyjs_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = zyjs_id
    WHERE
      `id` = zyjs_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (zyjs_id, 'police_zyjs', 1, '国家级人才工程', 0, '国家级人才工程', 1, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 2, '国家科学技奖', 0, '国家科学技奖', 2, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 3, '全国公安机关本专业业务技能大比武', 0, '全国公安机关本专业业务技能大比武', 3, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 4, '新型专利权', 0, '新型专利权', 4, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 5, '警务技术职务任职资格评委', 0, '警务技术职务任职资格评委', 5, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 6, '公安部警务技术专家库', 0, '公安部警务技术专家库', 6, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 7, '主持省（部）级以上科研项目研发工作', 0, '主持省（部）级以上科研项目研发工作', 7, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 8, '主持编著本专业培训教材课件', 0, '主持编著本专业培训教材课件', 8, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 9, '中文核心期刊上发表本专业论文', 0, '中文核心期刊上发表本专业论文', 9, NULL, NULL, NULL, 1),
    (zyjs_id, 'police_zyjs', 10, '参与起草制修订专业及相关专业标准', 0, '参与起草制修订专业及相关专业标准', 10, NULL, NULL, NULL, 1);
    -- ========== 立功受奖 (police_lgsj) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_lgsj_group', 0, '立功受奖', 0, '立功受奖', 1, NULL, NULL, NULL, 1);
    SET lgsj_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = lgsj_id
    WHERE
      `id` = lgsj_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (lgsj_id, 'police_lgsj', 1, '国家级劳模', 0, '国家级劳模', 1, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 2, '省劳模', 0, '省劳模', 2, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 3, '全国特级优秀人民警察', 0, '全国特级优秀人民警察', 3, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 4, '全国优秀人民警察', 0, '全国优秀人民警察', 4, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 5, '个人一等功', 0, '个人一等功', 5, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 6, '个人二等功', 0, '个人二等功', 6, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 7, '个人三等功', 0, '个人三等功', 7, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 8, '先进个人', 0, '先进个人', 8, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 9, '优秀共产党员', 0, '优秀共产党员', 9, NULL, NULL, NULL, 1),
    (lgsj_id, 'police_lgsj', 10, '优秀党务工作者', 0, '优秀党务工作者', 10, NULL, NULL, NULL, 1);
    -- ========== 问题类型 (police_wgwj) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_wgwj_group', 0, '问题类型', 0, '问题类型', 1, NULL, NULL, NULL, 1);
    SET wgwj_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = wgwj_id
    WHERE
      `id` = wgwj_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (wgwj_id, 'police_wgwj', 1, '行政警告', 0, '行政警告', 1, NULL, NULL, NULL, 1),
    (wgwj_id, 'police_wgwj', 2, '党内警告', 0, '党内警告', 2, NULL, NULL, NULL, 1);
    -- ========== 考核结果 (police_assessment_result) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_assessment_result_group', 0, '优秀', 0, '考核结果', 1, NULL, NULL, NULL, 1);
    SET khjg_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = khjg_id
    WHERE
      `id` = khjg_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (khjg_id, 'police_assessment_result', 1, '优秀', 1, '优秀', 1, NULL, NULL, NULL, 1),
    (khjg_id, 'police_assessment_result', 2, '称职', 1, '称职', 2, NULL, NULL, NULL, 1),
    (khjg_id, 'police_assessment_result', 3, '基本称职', 1, '基本称职', 3, NULL, NULL, NULL, 1),
    (khjg_id, 'police_assessment_result', 4, '不称职', 1, '不称职', 4, NULL, NULL, NULL, 1);
    -- =========晋升状态=================--
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_emergency_status_group', 0, '晋升状态', 0, '晋升状态', 1, NULL, NULL, NULL, 1);
    SET jszt_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = jszt_id
    WHERE
      `id` = jszt_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (jszt_id, 'police_emergency_status', 1, '符合晋升', 0, '符合晋升', 1, NULL, NULL, NULL, 1),
    (jszt_id, 'police_emergency_status', 2, '不符合晋升', 0, '不符合晋升', 2, NULL, NULL, NULL, 1),
    (jszt_id, 'police_emergency_status', 3, '不得晋升', 0, '不得晋升', 3, NULL, NULL, NULL, 1),
    (jszt_id, 'police_emergency_status', 4, '暂缓晋升', 0, '暂缓晋升', 4, NULL, NULL, NULL, 1);
    -- ============年龄范围=================== --
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('police_age_range_group', 0, '年龄范围', 0, '年龄范围', 1, NULL, NULL, NULL, 1);
    SET age_range_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = age_range_id
    WHERE
      `id` = age_range_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (age_range_id, 'police_age_range', 1, '30岁以下', 0, '30岁以下', 1, NULL, NULL, NULL, 1),
    (age_range_id, 'police_age_range', 2, '30-40岁', 0, '30-40岁', 2, NULL, NULL, NULL, 1),
    (age_range_id, 'police_age_range', 3, '40-50岁', 0, '40-50岁', 3, NULL, NULL, NULL, 1),
    (age_range_id, 'police_age_range', 4, '50-60岁', 0, '50-60岁', 4, NULL, NULL, NULL, 1),
    (age_range_id, 'police_age_range', 5, '60岁以上', 0, '60岁以上', 5, NULL, NULL, NULL, 1);
    -- ========== 职级 (police_zj) ==========
    INSERT INTO `t_dict` (`type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    ('t_degree_group', 0, '学历', 0, '学历', 1, NULL, NULL, NULL, 1);
    SET degree_id = LAST_INSERT_ID();
    UPDATE `t_dict`
    SET `p_id` = degree_id
    WHERE
      `id` = degree_id;
    INSERT INTO `t_dict` (`p_id`, `type`, `code`, `name`, `p_code`, `dict_desc`, `show_number`, `standard`, `flag`, `color`, `status`)
    VALUES
    (degree_id, 't_degree', 1, '小学', 0, '小学', 1, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 2, '初中', 0, '初中', 2, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 3, '高中', 0, '高中', 3, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 4, '大专', 0, '大专', 4, NULL, NULL, NULL, 1),
    (degree_id, 't_degree', 5, '大学本科', 0, '大学本科', 5, NULL, NULL, NULL, 1),
    (degree_id, 't_degree',6, '研究生', 0, '研究生', 6, NULL, NULL, NULL, 1);
END $$
DELIMITER ;
CALL insert_dict_data;
DROP PROCEDURE IF EXISTS insert_dict_data;