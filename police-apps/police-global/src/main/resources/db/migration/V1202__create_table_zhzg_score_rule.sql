create table if not exists t_zhzg_score_rule
(
    id                    bigint auto_increment primary key,
    create_time           timestamp         null,
    create_user_id        bigint            null,
    create_dept_id        bigint            null,
    update_time           timestamp         null,
    update_user_id        bigint            null,
    update_dept_id        bigint            null,
    name                  varchar(100)      not null comment '规则名称',
    description           text              null     comment '规则描述',
    parent_id             bigint            null comment '父id',
    score                 float(6, 3)       default 0.0    comment '规则分数',
    full_score            float(6, 3)       default 0.0    comment '满分',
    is_leaf               tinyint           not null comment '是否是叶子结点',
    is_enabled            tinyint           default 1 comment '是否启用',
    applicable_rank       int               null     comment '适用职级',
    rule_type             varchar(200)      null comment '规则类型，用来和内置规则匹配',
    rule_sub_type         varchar(200)      null comment '规则子类型，用来和内置规则匹配'
) comment '智慧政工积分规则表';


DELIMITER $$
DROP PROCEDURE IF EXISTS `add_column` $$
CREATE PROCEDURE add_column()
BEGIN
    IF NOT EXISTS(
        SELECT * FROM  information_schema.columns
        WHERE table_schema=(select database())
          AND table_name='t_profile_police' AND column_name='score_detail'
    )
    THEN
        ALTER TABLE t_profile_police ADD score_detail json COMMENT '积分详情';
    END IF;
END $$
DELIMITER ;
CALL add_column;
DROP PROCEDURE IF EXISTS `add_column`;